package com.hadoop.movieanalysis.job2;

import org.apache.hadoop.io.IntWritable;
import org.apache.hadoop.mapreduce.Reducer;

import java.io.IOException;

/**
 * 用户活跃度分析Combiner
 * 在Map端进行本地聚合，减少网络传输
 * 输入: Key=UserID, Value=1
 * 输出: Key=UserID, Value=本地计数总和
 */
public class UserActivityCombiner extends Reducer<IntWritable, IntWritable, IntWritable, IntWritable> {

    private IntWritable result = new IntWritable();

    @Override
    protected void reduce(IntWritable key, Iterable<IntWritable> values, Context context)
            throws IOException, InterruptedException {

        int sum = 0;
        
        // 计算本地总和
        for (IntWritable value : values) {
            sum += value.get();
        }

        result.set(sum);
        context.write(key, result);
        
        // 更新计数器
        context.getCounter("UserActivityCombiner", "USERS_PROCESSED").increment(1);
    }
}
