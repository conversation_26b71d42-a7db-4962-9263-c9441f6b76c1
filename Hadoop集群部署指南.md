# Hadoop集群完整部署运行教程 - 电影分析MapReduce项目

## 🎯 教程概述

本教程提供了在Hadoop集群上部署和运行电影分析MapReduce项目的**最完整、最详细**的步骤指南。每一步都包含具体的路径、命令和预期输出，确保您能够成功完成部署。

**重要说明**: 本教程适用于**非本地部署**，即在真实的Hadoop集群环境中运行，**无需Maven环境**。

## 📋 环境要求

- **Hadoop版本**: 3.1.3
- **Java版本**: 8 或更高版本（推荐Java 8）
- **操作系统**: Linux/Unix（CentOS 7/8, Ubuntu 18.04+）
- **内存**: 至少4GB可用内存（推荐8GB+）
- **存储**: 至少10GB可用磁盘空间
- **网络**: 集群节点间网络互通
- **用户权限**: 具有Hadoop操作权限的用户账户

## 🚀 完整部署步骤

### 第一阶段：环境准备和检查

#### 步骤1.1：登录Hadoop集群

```bash
# 使用SSH登录到Hadoop集群的主节点（NameNode）
# 替换为您的实际IP地址和用户名
ssh hadoop@*************

# 或者如果您已经在集群节点上，切换到hadoop用户
su - hadoop
```

**预期输出**:
```
Last login: Mon Dec 25 10:00:00 2023 from ************
[hadoop@namenode ~]$
```

#### 步骤1.2：检查当前工作目录

```bash
# 查看当前所在目录
pwd

# 查看当前用户
whoami

# 查看用户主目录内容
ls -la ~
```

**预期输出**:
```
/home/<USER>
hadoop
total 24
drwx------. 3 <USER> <GROUP> 4096 Dec 25 10:00 .
drwxr-xr-x. 3 <USER>   <GROUP>   4096 Dec 20 15:30 ..
-rw-------. 1 <USER> <GROUP>  123 Dec 25 09:58 .bash_history
-rw-r--r--. 1 <USER> <GROUP>   18 Dec 20 15:30 .bash_logout
-rw-r--r--. 1 <USER> <GROUP>  193 Dec 20 15:30 .bash_profile
-rw-r--r--. 1 <USER> <GROUP>  231 Dec 20 15:30 .bashrc
```

#### 步骤1.3：检查Java环境

```bash
# 检查Java版本
java -version

# 检查Java编译器
javac -version

# 检查JAVA_HOME环境变量
echo "JAVA_HOME: $JAVA_HOME"

# 如果JAVA_HOME未设置，查找Java安装路径
which java
ls -la $(which java)
```

**预期输出**:
```
openjdk version "1.8.0_332"
OpenJDK Runtime Environment (build 1.8.0_332-b09)
OpenJDK 64-Bit Server VM (build 25.332-b09, mixed mode)

javac 1.8.0_332

JAVA_HOME: /usr/lib/jvm/java-1.8.0-openjdk

/usr/bin/java
lrwxrwxrwx. 1 root root 22 Dec 20 15:25 /usr/bin/java -> /etc/alternatives/java
```

**如果JAVA_HOME未设置，执行以下命令**:
```bash
# 设置JAVA_HOME（临时）
export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk

# 永久设置（添加到.bashrc）
echo 'export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk' >> ~/.bashrc
source ~/.bashrc
```

#### 步骤1.4：检查Hadoop环境

```bash
# 检查Hadoop版本
hadoop version

# 检查HADOOP_HOME环境变量
echo "HADOOP_HOME: $HADOOP_HOME"

# 检查Hadoop配置目录
echo "HADOOP_CONF_DIR: $HADOOP_CONF_DIR"

# 查看Hadoop安装目录
ls -la $HADOOP_HOME
```

**预期输出**:
```
Hadoop 3.1.3
Source code repository https://gitbox.apache.org/repos/asf/hadoop.git -r ba631c436b806728f8ec2f54ab1e289526c90579
Compiled by ztang on 2019-09-12T02:47Z
Compiled with protoc 2.5.0
From source with checksum ec785077c385118ac91aadde5ec9799

HADOOP_HOME: /opt/hadoop-3.1.3
HADOOP_CONF_DIR: /opt/hadoop-3.1.3/etc/hadoop

total 148
drwxr-xr-x. 9 <USER> <GROUP>  4096 Dec 20 15:40 .
drwxr-xr-x. 3 <USER>   <GROUP>    4096 Dec 20 15:35 ..
drwxr-xr-x. 2 <USER> <GROUP>  4096 Dec 20 15:40 bin
drwxr-xr-x. 3 <USER> <GROUP>  4096 Dec 20 15:40 etc
drwxr-xr-x. 2 <USER> <GROUP>  4096 Dec 20 15:40 include
drwxr-xr-x. 3 <USER> <GROUP>  4096 Dec 20 15:40 lib
drwxr-xr-x. 2 <USER> <GROUP>  4096 Dec 20 15:40 libexec
-rw-r--r--. 1 <USER> <GROUP> 52311 Sep 12  2019 LICENSE.txt
-rw-r--r--. 1 <USER> <GROUP>   101 Sep 12  2019 NOTICE.txt
-rw-r--r--. 1 <USER> <GROUP>  1366 Sep 12  2019 README.txt
drwxr-xr-x. 2 <USER> <GROUP>  4096 Dec 20 15:40 sbin
drwxr-xr-x. 4 <USER> <GROUP>  4096 Dec 20 15:40 share
```

#### 步骤1.5：检查Hadoop集群状态

```bash
# 检查HDFS状态
hdfs dfsadmin -report

# 检查YARN状态
yarn node -list

# 查看正在运行的Java进程
jps
```

**预期输出**:
```
Configured Capacity: 107374182400 (100.00 GB)
Present Capacity: 96636764160 (90.00 GB)
DFS Remaining: 85899345920 (80.00 GB)
DFS Used: 10737418240 (10.00 GB)
...

Total Nodes: 3
         Node-Id	     Node-State	Node-Http-Address	Number-of-Running-Containers
   worker1:8041	        RUNNING	   worker1:8042	                           0
   worker2:8041	        RUNNING	   worker2:8042	                           0
   worker3:8041	        RUNNING	   worker3:8042	                           0

12345 NameNode
12346 DataNode
12347 SecondaryNameNode
12348 ResourceManager
12349 NodeManager
12350 Jps
```

### 第二阶段：项目上传和编译

#### 步骤2.1：创建项目工作目录

```bash
# 在用户主目录下创建项目目录
cd ~
mkdir -p movie-analysis-project
cd movie-analysis-project

# 查看当前路径
pwd
```

**预期输出**:
```
/home/<USER>/movie-analysis-project
```

#### 步骤2.2：上传项目文件到集群

**方法1：使用scp从本地上传（推荐）**
```bash
# 在本地机器上执行（将整个项目目录上传）
# 替换为您的实际IP地址
scp -r /path/to/mjyhadoop hadoop@*************:/home/<USER>/movie-analysis-project/

# 或者分别上传关键文件
scp -r /path/to/mjyhadoop/src hadoop@*************:/home/<USER>/movie-analysis-project/
scp -r /path/to/mjyhadoop/input hadoop@*************:/home/<USER>/movie-analysis-project/
scp /path/to/mjyhadoop/pom.xml hadoop@*************:/home/<USER>/movie-analysis-project/
```

**方法2：使用git克隆（如果项目在git仓库中）**
```bash
# 在集群上直接克隆项目
cd /home/<USER>/movie-analysis-project
git clone https://github.com/your-repo/movie-analysis.git .
```

**方法3：手动创建项目结构（如果无法上传）**
```bash
# 创建项目目录结构
mkdir -p src/main/java/com/hadoop/movieanalysis/{model,writable,job1,job2,job3}
mkdir -p input
mkdir -p target/classes

# 然后手动创建或复制Java源文件和数据文件
```

#### 步骤2.3：验证项目文件

```bash
# 进入项目目录
cd /home/<USER>/movie-analysis-project

# 查看项目结构
find . -type f -name "*.java" | head -10
find . -type f -name "*.dat"

# 查看具体的目录结构
tree . || ls -la
```

**预期输出**:
```
./src/main/java/com/hadoop/movieanalysis/MovieAnalysisDriver.java
./src/main/java/com/hadoop/movieanalysis/model/Movie.java
./src/main/java/com/hadoop/movieanalysis/model/Rating.java
./src/main/java/com/hadoop/movieanalysis/model/User.java
./src/main/java/com/hadoop/movieanalysis/writable/MovieRatingKey.java
./src/main/java/com/hadoop/movieanalysis/writable/RatingStatistics.java
./src/main/java/com/hadoop/movieanalysis/job1/MovieRatingStatsMapper.java
./src/main/java/com/hadoop/movieanalysis/job1/MovieRatingStatsCombiner.java
./src/main/java/com/hadoop/movieanalysis/job1/MovieRatingStatsReducer.java
./src/main/java/com/hadoop/movieanalysis/job1/MovieRatingStatsPartitioner.java

./input/movies.dat
./input/ratings.dat
./input/users.dat
```

#### 步骤2.4：检查数据文件

```bash
# 检查数据文件大小
ls -lh input/

# 查看数据文件前几行，确认格式正确
echo "=== movies.dat 前5行 ==="
head -5 input/movies.dat

echo "=== ratings.dat 前5行 ==="
head -5 input/ratings.dat

echo "=== users.dat 前5行 ==="
head -5 input/users.dat

# 统计数据行数
echo "=== 数据统计 ==="
wc -l input/*.dat
```

**预期输出**:
```
-rw-r--r--. 1 <USER> <GROUP>  171K Dec 25 10:30 input/movies.dat
-rw-r--r--. 1 <USER> <GROUP>   24M Dec 25 10:30 input/ratings.dat
-rw-r--r--. 1 <USER> <GROUP>  134K Dec 25 10:30 input/users.dat

=== movies.dat 前5行 ===
1::Toy Story (1995)::Animation|Children's|Comedy
2::Jumanji (1995)::Adventure|Children's|Fantasy
3::Grumpier Old Men (1995)::Comedy|Romance
4::Waiting to Exhale (1995)::Comedy|Drama
5::Father of the Bride Part II (1995)::Comedy

=== ratings.dat 前5行 ===
1::1193::5::978300760
1::661::3::978302109
1::914::3::978301968
1::3408::4::978300275
1::2355::5::978824291

=== users.dat 前5行 ===
1::F::1::10::48067
2::M::56::16::70072
3::M::25::15::55117
4::M::45::7::02460
5::M::25::20::55455

=== 数据统计 ===
   3883 input/movies.dat
1000209 input/ratings.dat
   6040 input/users.dat
```

#### 步骤2.5：编译MapReduce项目

**设置编译环境**:
```bash
# 确保在项目根目录
cd /home/<USER>/movie-analysis-project
pwd

# 设置Hadoop类路径用于编译
export HADOOP_CLASSPATH=$JAVA_HOME/lib/tools.jar
echo "HADOOP_CLASSPATH: $HADOOP_CLASSPATH"

# 创建编译输出目录
mkdir -p target/classes
```

**编译步骤1：编译数据模型包**
```bash
echo "编译数据模型包..."
hadoop com.sun.tools.javac.Main -d target/classes -sourcepath src/main/java src/main/java/com/hadoop/movieanalysis/model/*.java

# 检查编译结果
ls -la target/classes/com/hadoop/movieanalysis/model/
```

**预期输出**:
```
编译数据模型包...
total 12
drwxr-xr-x. 2 <USER> <GROUP> 4096 Dec 25 10:35 .
drwxr-xr-x. 5 <USER> <GROUP> 4096 Dec 25 10:35 ..
-rw-r--r--. 1 <USER> <GROUP> 1234 Dec 25 10:35 Movie.class
-rw-r--r--. 1 <USER> <GROUP> 1456 Dec 25 10:35 Rating.class
-rw-r--r--. 1 <USER> <GROUP> 1123 Dec 25 10:35 User.class
```

**编译步骤2：编译自定义Writable类**
```bash
echo "编译自定义Writable类..."
hadoop com.sun.tools.javac.Main -d target/classes -sourcepath src/main/java src/main/java/com/hadoop/movieanalysis/writable/*.java

# 检查编译结果
ls -la target/classes/com/hadoop/movieanalysis/writable/
```

**编译步骤3：编译MapReduce任务**
```bash
echo "编译MapReduce任务1..."
hadoop com.sun.tools.javac.Main -d target/classes -sourcepath src/main/java src/main/java/com/hadoop/movieanalysis/job1/*.java

echo "编译MapReduce任务2..."
hadoop com.sun.tools.javac.Main -d target/classes -sourcepath src/main/java src/main/java/com/hadoop/movieanalysis/job2/*.java

echo "编译MapReduce任务3..."
hadoop com.sun.tools.javac.Main -d target/classes -sourcepath src/main/java src/main/java/com/hadoop/movieanalysis/job3/*.java

# 检查编译结果
ls -la target/classes/com/hadoop/movieanalysis/job*/
```

**编译步骤4：编译主驱动程序**
```bash
echo "编译主驱动程序..."
hadoop com.sun.tools.javac.Main -d target/classes -sourcepath src/main/java src/main/java/com/hadoop/movieanalysis/*.java

# 检查编译结果
ls -la target/classes/com/hadoop/movieanalysis/
```

#### 步骤2.6：创建JAR文件

```bash
echo "创建JAR文件..."

# 进入编译输出目录
cd target/classes

# 查看编译后的类文件结构
find com -name "*.class" | head -10

# 创建JAR文件
jar cf ../movie-analysis.jar com/

# 返回项目根目录
cd ../..

# 验证JAR文件
ls -lh target/movie-analysis.jar
jar tf target/movie-analysis.jar | head -10
```

**预期输出**:
```
创建JAR文件...
com/hadoop/movieanalysis/MovieAnalysisDriver.class
com/hadoop/movieanalysis/model/Movie.class
com/hadoop/movieanalysis/model/Rating.class
com/hadoop/movieanalysis/model/User.class
com/hadoop/movieanalysis/writable/MovieRatingKey.class
com/hadoop/movieanalysis/writable/RatingStatistics.class
com/hadoop/movieanalysis/job1/MovieRatingStatsMapper.class
com/hadoop/movieanalysis/job1/MovieRatingStatsCombiner.class
com/hadoop/movieanalysis/job1/MovieRatingStatsReducer.class
com/hadoop/movieanalysis/job1/MovieRatingStatsPartitioner.class

-rw-r--r--. 1 <USER> <GROUP> 15K Dec 25 10:40 target/movie-analysis.jar

com/
com/hadoop/
com/hadoop/movieanalysis/
com/hadoop/movieanalysis/MovieAnalysisDriver.class
com/hadoop/movieanalysis/model/
com/hadoop/movieanalysis/model/Movie.class
com/hadoop/movieanalysis/model/Rating.class
com/hadoop/movieanalysis/model/User.class
com/hadoop/movieanalysis/writable/
com/hadoop/movieanalysis/writable/MovieRatingKey.class
```

### 第三阶段：数据上传到HDFS

#### 步骤3.1：清理HDFS旧数据（如果存在）

```bash
# 确保在项目根目录
cd /home/<USER>/movie-analysis-project
pwd

# 检查HDFS根目录
hdfs dfs -ls /

# 删除可能存在的旧数据（忽略错误）
hdfs dfs -rm -r /input 2>/dev/null || echo "目录 /input 不存在，跳过删除"
hdfs dfs -rm -r /output 2>/dev/null || echo "目录 /output 不存在，跳过删除"

# 再次检查HDFS根目录
hdfs dfs -ls /
```

**预期输出**:
```
/home/<USER>/movie-analysis-project

Found 3 items
drwxr-xr-x   - hadoop supergroup          0 2023-12-20 15:45 /tmp
drwxr-xr-x   - hadoop supergroup          0 2023-12-20 15:45 /user
drwxr-xr-x   - hadoop supergroup          0 2023-12-20 15:45 /var

目录 /input 不存在，跳过删除
目录 /output 不存在，跳过删除

Found 3 items
drwxr-xr-x   - hadoop supergroup          0 2023-12-20 15:45 /tmp
drwxr-xr-x   - hadoop supergroup          0 2023-12-20 15:45 /user
drwxr-xr-x   - hadoop supergroup          0 2023-12-20 15:45 /var
```

#### 步骤3.2：创建HDFS输入目录

```bash
# 创建输入目录
hdfs dfs -mkdir /input

# 验证目录创建
hdfs dfs -ls /

# 检查目录权限
hdfs dfs -ls -d /input
```

**预期输出**:
```
Found 4 items
drwxr-xr-x   - hadoop supergroup          0 2023-12-25 10:45 /input
drwxr-xr-x   - hadoop supergroup          0 2023-12-20 15:45 /tmp
drwxr-xr-x   - hadoop supergroup          0 2023-12-20 15:45 /user
drwxr-xr-x   - hadoop supergroup          0 2023-12-20 15:45 /var

drwxr-xr-x   - hadoop supergroup          0 2023-12-25 10:45 /input
```

#### 步骤3.3：上传数据文件到HDFS

```bash
# 上传电影数据文件
echo "上传 movies.dat..."
hdfs dfs -put input/movies.dat /input/
echo "movies.dat 上传完成"

# 上传评分数据文件
echo "上传 ratings.dat..."
hdfs dfs -put input/ratings.dat /input/
echo "ratings.dat 上传完成"

# 上传用户数据文件
echo "上传 users.dat..."
hdfs dfs -put input/users.dat /input/
echo "users.dat 上传完成"
```

**预期输出**:
```
上传 movies.dat...
movies.dat 上传完成
上传 ratings.dat...
ratings.dat 上传完成
上传 users.dat...
users.dat 上传完成
```

#### 步骤3.4：验证HDFS上传结果

```bash
# 查看HDFS输入目录内容
hdfs dfs -ls /input

# 查看文件大小
hdfs dfs -du -h /input

# 检查文件内容（查看前几行）
echo "=== HDFS中 movies.dat 前5行 ==="
hdfs dfs -cat /input/movies.dat | head -5

echo "=== HDFS中 ratings.dat 前5行 ==="
hdfs dfs -cat /input/ratings.dat | head -5

echo "=== HDFS中 users.dat 前5行 ==="
hdfs dfs -cat /input/users.dat | head -5

# 统计HDFS中的数据行数
echo "=== HDFS数据统计 ==="
echo "movies.dat 行数: $(hdfs dfs -cat /input/movies.dat | wc -l)"
echo "ratings.dat 行数: $(hdfs dfs -cat /input/ratings.dat | wc -l)"
echo "users.dat 行数: $(hdfs dfs -cat /input/users.dat | wc -l)"
```

**预期输出**:
```
Found 3 items
-rw-r--r--   3 <USER> <GROUP>     171308 2023-12-25 10:50 /input/movies.dat
-rw-r--r--   3 <USER> <GROUP>   24594131 2023-12-25 10:50 /input/ratings.dat
-rw-r--r--   3 <USER> <GROUP>     134368 2023-12-25 10:50 /input/users.dat

167.2 K  /input/movies.dat
23.5 M   /input/ratings.dat
131.2 K  /input/users.dat

=== HDFS中 movies.dat 前5行 ===
1::Toy Story (1995)::Animation|Children's|Comedy
2::Jumanji (1995)::Adventure|Children's|Fantasy
3::Grumpier Old Men (1995)::Comedy|Romance
4::Waiting to Exhale (1995)::Comedy|Drama
5::Father of the Bride Part II (1995)::Comedy

=== HDFS中 ratings.dat 前5行 ===
1::1193::5::978300760
1::661::3::978302109
1::914::3::978301968
1::3408::4::978300275
1::2355::5::978824291

=== HDFS中 users.dat 前5行 ===
1::F::1::10::48067
2::M::56::16::70072
3::M::25::15::55117
4::M::45::7::02460
5::M::25::20::55455

=== HDFS数据统计 ===
movies.dat 行数: 3883
ratings.dat 行数: 1000209
users.dat 行数: 6040
```

### 第四阶段：提交MapReduce任务

#### 步骤4.1：准备任务提交

```bash
# 确保在项目根目录
cd /home/<USER>/movie-analysis-project
pwd

# 验证JAR文件存在
ls -lh target/movie-analysis.jar

# 验证HDFS输入数据存在
hdfs dfs -ls /input

# 检查当前YARN集群状态
yarn node -list
```

**预期输出**:
```
/home/<USER>/movie-analysis-project

-rw-r--r--. 1 <USER> <GROUP> 15K Dec 25 10:40 target/movie-analysis.jar

Found 3 items
-rw-r--r--   3 <USER> <GROUP>     171308 2023-12-25 10:50 /input/movies.dat
-rw-r--r--   3 <USER> <GROUP>   24594131 2023-12-25 10:50 /input/ratings.dat
-rw-r--r--   3 <USER> <GROUP>     134368 2023-12-25 10:50 /input/users.dat

Total Nodes: 3
         Node-Id	     Node-State	Node-Http-Address	Number-of-Running-Containers
   worker1:8041	        RUNNING	   worker1:8042	                           0
   worker2:8041	        RUNNING	   worker2:8042	                           0
   worker3:8041	        RUNNING	   worker3:8042	                           0
```

#### 步骤4.2：提交所有MapReduce任务

```bash
# 记录开始时间
echo "开始时间: $(date)"
start_time=$(date +%s)

# 提交所有任务到Hadoop集群
echo "========================================="
echo "提交电影分析MapReduce任务到Hadoop集群"
echo "========================================="

hadoop jar target/movie-analysis.jar com.hadoop.movieanalysis.MovieAnalysisDriver /input /output all

# 记录结束时间
end_time=$(date +%s)
duration=$((end_time - start_time))
echo "结束时间: $(date)"
echo "总执行时间: ${duration}秒"
```

**预期输出（任务执行过程）**:
```
开始时间: Mon Dec 25 11:00:00 CST 2023
=========================================
提交电影分析MapReduce任务到Hadoop集群
=========================================

开始运行任务1: 电影评分统计分析
23/12/25 11:00:05 INFO client.RMProxy: Connecting to ResourceManager at namenode/*************:8032
23/12/25 11:00:06 INFO mapreduce.JobResourceUploader: Disabling Erasure Coding for path: /tmp/hadoop-yarn/staging/hadoop/.staging/job_1703476800000_0001
23/12/25 11:00:06 INFO input.FileInputFormat: Total input files to process : 1
23/12/25 11:00:06 INFO mapreduce.JobSubmitter: number of splits:8
23/12/25 11:00:06 INFO Configuration.deprecation: yarn.resourcemanager.system-metrics-publisher.enabled is deprecated. Instead, use yarn.system-metrics-publisher.enabled
23/12/25 11:00:07 INFO mapreduce.JobSubmitter: Submitting tokens for job: job_1703476800000_0001
23/12/25 11:00:07 INFO mapreduce.JobSubmitter: Executing with tokens: []
23/12/25 11:00:07 INFO conf.Configuration: resource-types.xml not found
23/12/25 11:00:07 INFO resource.ResourceUtils: Unable to find 'resource-types.xml'.
23/12/25 11:00:07 INFO impl.YarnClientImpl: Submitted application application_1703476800000_0001
23/12/25 11:00:07 INFO mapreduce.Job: The url to track the job: http://namenode:8088/proxy/application_1703476800000_0001/
23/12/25 11:00:07 INFO mapreduce.Job: Running job: job_1703476800000_0001
23/12/25 11:00:15 INFO mapreduce.Job: Job job_1703476800000_0001 running in uber mode : false
23/12/25 11:00:15 INFO mapreduce.Job:  map 0% reduce 0%
23/12/25 11:00:25 INFO mapreduce.Job:  map 25% reduce 0%
23/12/25 11:00:35 INFO mapreduce.Job:  map 50% reduce 0%
23/12/25 11:00:45 INFO mapreduce.Job:  map 75% reduce 0%
23/12/25 11:00:55 INFO mapreduce.Job:  map 100% reduce 0%
23/12/25 11:01:05 INFO mapreduce.Job:  map 100% reduce 25%
23/12/25 11:01:15 INFO mapreduce.Job:  map 100% reduce 50%
23/12/25 11:01:25 INFO mapreduce.Job:  map 100% reduce 75%
23/12/25 11:01:35 INFO mapreduce.Job:  map 100% reduce 100%
23/12/25 11:01:36 INFO mapreduce.Job: Job job_1703476800000_0001 completed successfully
23/12/25 11:01:36 INFO mapreduce.Job: Counters: 54
	File System Counters
		FILE: Number of bytes read=15234567
		FILE: Number of bytes written=30469134
		HDFS: Number of bytes read=24594259
		HDFS: Number of bytes written=234567
		...
	Map-Reduce Framework
		Map input records=1000209
		Map output records=1000209
		Map output bytes=12002508
		Map output materialized bytes=13202759
		Input split bytes=128
		Combine input records=1000209
		Combine output records=3706
		Reduce input groups=3706
		Reduce shuffle bytes=13202759
		Reduce input records=3706
		Reduce output records=3706
		...

任务1完成，状态: 成功

开始运行任务2: 用户活跃度分析
[类似的日志输出...]
任务2完成，状态: 成功

开始运行任务3: 电影类型偏好分析
[类似的日志输出...]
任务3完成，状态: 成功

结束时间: Mon Dec 25 11:05:30 CST 2023
总执行时间: 330秒
```

#### 步骤4.3：验证任务执行结果

```bash
# 检查HDFS输出目录
echo "========================================="
echo "检查HDFS输出目录结构"
echo "========================================="
hdfs dfs -ls /output

# 检查各个任务的输出文件
echo "========================================="
echo "检查各任务输出文件"
echo "========================================="

for job in job1_movie_stats job2_user_activity job3_genre_preference; do
    echo "--- 检查 $job ---"
    hdfs dfs -ls /output/$job
    if hdfs dfs -test -e /output/$job/part-r-00000; then
        echo "✓ $job 输出文件存在"
        file_size=$(hdfs dfs -du -h /output/$job/part-r-00000 | awk '{print $1}')
        echo "✓ 文件大小: $file_size"
    else
        echo "✗ $job 输出文件不存在"
    fi
    echo
done
```

**预期输出**:
```
=========================================
检查HDFS输出目录结构
=========================================
Found 3 items
drwxr-xr-x   - hadoop supergroup          0 2023-12-25 11:01 /output/job1_movie_stats
drwxr-xr-x   - hadoop supergroup          0 2023-12-25 11:03 /output/job2_user_activity
drwxr-xr-x   - hadoop supergroup          0 2023-12-25 11:05 /output/job3_genre_preference

=========================================
检查各任务输出文件
=========================================
--- 检查 job1_movie_stats ---
Found 2 items
-rw-r--r--   3 <USER> <GROUP>          0 2023-12-25 11:01 /output/job1_movie_stats/_SUCCESS
-rw-r--r--   3 <USER> <GROUP>     234567 2023-12-25 11:01 /output/job1_movie_stats/part-r-00000
✓ job1_movie_stats 输出文件存在
✓ 文件大小: 229.1 K

--- 检查 job2_user_activity ---
Found 2 items
-rw-r--r--   3 <USER> <GROUP>          0 2023-12-25 11:03 /output/job2_user_activity/_SUCCESS
-rw-r--r--   3 <USER> <GROUP>     123456 2023-12-25 11:03 /output/job2_user_activity/part-r-00000
✓ job2_user_activity 输出文件存在
✓ 文件大小: 120.6 K

--- 检查 job3_genre_preference ---
Found 2 items
-rw-r--r--   3 <USER> <GROUP>          0 2023-12-25 11:05 /output/job3_genre_preference/_SUCCESS
-rw-r--r--   3 <USER> <GROUP>       1234 2023-12-25 11:05 /output/job3_genre_preference/part-r-00000
✓ job3_genre_preference 输出文件存在
✓ 文件大小: 1.2 K
```

#### 步骤4.4：运行单个任务（可选）

如果您想单独运行某个任务，可以使用以下命令：

```bash
# 清理输出目录
hdfs dfs -rm -r /output

# 任务1：电影评分统计分析
echo "运行任务1：电影评分统计分析"
hadoop jar target/movie-analysis.jar com.hadoop.movieanalysis.MovieAnalysisDriver /input /output 1

# 任务2：用户活跃度分析
echo "运行任务2：用户活跃度分析"
hadoop jar target/movie-analysis.jar com.hadoop.movieanalysis.MovieAnalysisDriver /input /output 2

# 任务3：电影类型偏好分析
echo "运行任务3：电影类型偏好分析"
hadoop jar target/movie-analysis.jar com.hadoop.movieanalysis.MovieAnalysisDriver /input /output 3
```

### 第五阶段：查看和分析结果

#### 步骤5.1：查看任务1结果（电影评分统计分析）

```bash
echo "========================================="
echo "任务1：电影评分统计分析结果"
echo "========================================="

# 查看结果文件信息
hdfs dfs -ls /output/job1_movie_stats/

# 统计结果行数
echo "结果总行数: $(hdfs dfs -cat /output/job1_movie_stats/part-r-00000 | wc -l)"

# 查看前20行结果
echo "前20行结果:"
hdfs dfs -cat /output/job1_movie_stats/part-r-00000 | head -20

# 查看评分最高的电影（按平均评分排序）
echo "评分最高的电影（前10部）:"
hdfs dfs -cat /output/job1_movie_stats/part-r-00000 | \
awk -F'\t' '{print $2 "\t" $1}' | \
awk -F'平均评分: ' '{print $2 "\t" $1}' | \
awk -F',' '{print $1 "\t" $2}' | \
sort -nr | head -10

# 查看评分最多的电影（按评分总数排序）
echo "评分最多的电影（前10部）:"
hdfs dfs -cat /output/job1_movie_stats/part-r-00000 | \
awk -F'\t' '{print $2 "\t" $1}' | \
awk -F'评分总数: ' '{print $2 "\t" $1}' | \
awk -F',' '{print $1 "\t" $2}' | \
sort -nr | head -10
```

**预期输出**:
```
=========================================
任务1：电影评分统计分析结果
=========================================
Found 2 items
-rw-r--r--   3 <USER> <GROUP>          0 2023-12-25 11:01 /output/job1_movie_stats/_SUCCESS
-rw-r--r--   3 <USER> <GROUP>     234567 2023-12-25 11:01 /output/job1_movie_stats/part-r-00000

结果总行数: 3706

前20行结果:
1	评分总数: 2077, 平均评分: 4.15, 最高评分: 5.0, 最低评分: 1.0, 评分总和: 8619.00
2	评分总数: 701, 平均评分: 3.20, 最高评分: 5.0, 最低评分: 1.0, 评分总和: 2244.00
3	评分总数: 478, 平均评分: 3.02, 最高评分: 5.0, 最低评分: 1.0, 评分总和: 1443.00
4	评分总数: 170, 平均评分: 2.86, 最高评分: 5.0, 最低评分: 1.0, 评分总和: 487.00
5	评分总数: 296, 平均评分: 3.18, 最高评分: 5.0, 最低评分: 1.0, 评分总和: 941.00
...

评分最高的电影（前10部）:
4.85	电影ID: 1234
4.78	电影ID: 5678
4.65	电影ID: 9012
...

评分最多的电影（前10部）:
2992	电影ID: 260
2077	电影ID: 1
1865	电影ID: 1196
...
```

#### 步骤5.2：查看任务2结果（用户活跃度分析）

```bash
echo "========================================="
echo "任务2：用户活跃度分析结果"
echo "========================================="

# 查看结果文件信息
hdfs dfs -ls /output/job2_user_activity/

# 统计结果行数
echo "结果总行数: $(hdfs dfs -cat /output/job2_user_activity/part-r-00000 | wc -l)"

# 查看前20行结果
echo "前20行结果:"
hdfs dfs -cat /output/job2_user_activity/part-r-00000 | head -20

# 统计各活跃度等级的用户数量
echo "用户活跃度分布统计:"
echo "非常活跃用户数: $(hdfs dfs -cat /output/job2_user_activity/part-r-00000 | grep '非常活跃' | wc -l)"
echo "活跃用户数: $(hdfs dfs -cat /output/job2_user_activity/part-r-00000 | grep -w '活跃' | wc -l)"
echo "一般活跃用户数: $(hdfs dfs -cat /output/job2_user_activity/part-r-00000 | grep '一般活跃' | wc -l)"
echo "不活跃用户数: $(hdfs dfs -cat /output/job2_user_activity/part-r-00000 | grep '不活跃' | wc -l)"

# 查看最活跃的用户（前10名）
echo "最活跃的用户（前10名）:"
hdfs dfs -cat /output/job2_user_activity/part-r-00000 | \
awk -F'\t' '{print $2 "\t" $1}' | \
awk -F'评分次数: ' '{print $2 "\t" $1}' | \
awk -F',' '{print $1 "\t" $2}' | \
sort -nr | head -10
```

**预期输出**:
```
=========================================
任务2：用户活跃度分析结果
=========================================
Found 2 items
-rw-r--r--   3 <USER> <GROUP>          0 2023-12-25 11:03 /output/job2_user_activity/_SUCCESS
-rw-r--r--   3 <USER> <GROUP>     123456 2023-12-25 11:03 /output/job2_user_activity/part-r-00000

结果总行数: 6040

前20行结果:
1	评分次数: 53, 活跃度: 一般活跃
2	评分次数: 182, 活跃度: 非常活跃
3	评分次数: 27, 活跃度: 一般活跃
4	评分次数: 35, 活跃度: 一般活跃
5	评分次数: 48, 活跃度: 一般活跃
...

用户活跃度分布统计:
非常活跃用户数: 1543
活跃用户数: 1089
一般活跃用户数: 2145
不活跃用户数: 1263

最活跃的用户（前10名）:
1226	用户ID: 424
870	用户ID: 352
825	用户ID: 482
...
```

#### 步骤5.3：查看任务3结果（电影类型偏好分析）

```bash
echo "========================================="
echo "任务3：电影类型偏好分析结果"
echo "========================================="

# 查看结果文件信息
hdfs dfs -ls /output/job3_genre_preference/

# 统计结果行数
echo "结果总行数: $(hdfs dfs -cat /output/job3_genre_preference/part-r-00000 | wc -l)"

# 查看完整结果（按平均评分排序）
echo "电影类型偏好分析结果（按平均评分排序）:"
hdfs dfs -cat /output/job3_genre_preference/part-r-00000

# 查看评分数量最多的类型
echo "评分数量最多的电影类型（前5名）:"
hdfs dfs -cat /output/job3_genre_preference/part-r-00000 | \
awk -F'\t' '{print $2 "\t" $1}' | \
awk -F'评分总数: ' '{print $2 "\t" $1}' | \
awk -F',' '{print $1 "\t" $2}' | \
sort -nr | head -5
```

**预期输出**:
```
=========================================
任务3：电影类型偏好分析结果
=========================================
Found 2 items
-rw-r--r--   3 <USER> <GROUP>          0 2023-12-25 11:05 /output/job3_genre_preference/_SUCCESS
-rw-r--r--   3 <USER> <GROUP>       1234 2023-12-25 11:05 /output/job3_genre_preference/part-r-00000

结果总行数: 18

电影类型偏好分析结果（按平均评分排序）:
Film-Noir	评分总数: 18262, 平均评分: 4.08, 最高评分: 5.0, 最低评分: 1.0
Documentary	评分总数: 7912, 平均评分: 3.93, 最高评分: 5.0, 最低评分: 1.0
War	评分总数: 68531, 平均评分: 3.89, 最高评分: 5.0, 最低评分: 1.0
Drama	评分总数: 355374, 平均评分: 3.77, 最高评分: 5.0, 最低评分: 1.0
Crime	评分总数: 79001, 平均评分: 3.70, 最高评分: 5.0, 最低评分: 1.0
...

评分数量最多的电影类型（前5名）:
357155	Comedy
355374	Drama
256132	Action
...
```

#### 步骤5.4：下载结果到本地

```bash
echo "========================================="
echo "下载结果到本地"
echo "========================================="

# 创建本地结果目录
mkdir -p /home/<USER>/movie-analysis-project/hadoop_results
cd /home/<USER>/movie-analysis-project

# 下载所有结果
echo "下载任务1结果..."
hdfs dfs -get /output/job1_movie_stats/part-r-00000 hadoop_results/job1_movie_stats.txt

echo "下载任务2结果..."
hdfs dfs -get /output/job2_user_activity/part-r-00000 hadoop_results/job2_user_activity.txt

echo "下载任务3结果..."
hdfs dfs -get /output/job3_genre_preference/part-r-00000 hadoop_results/job3_genre_preference.txt

# 验证下载结果
echo "本地结果文件:"
ls -lh hadoop_results/

# 查看本地文件内容摘要
echo "本地文件内容摘要:"
for file in hadoop_results/*.txt; do
    echo "--- $(basename $file) ---"
    echo "行数: $(wc -l < $file)"
    echo "文件大小: $(ls -lh $file | awk '{print $5}')"
    echo "前3行内容:"
    head -3 "$file"
    echo
done
```

**预期输出**:
```
=========================================
下载结果到本地
=========================================
下载任务1结果...
下载任务2结果...
下载任务3结果...

本地结果文件:
-rw-r--r--. 1 <USER> <GROUP> 229K Dec 25 11:10 hadoop_results/job1_movie_stats.txt
-rw-r--r--. 1 <USER> <GROUP> 121K Dec 25 11:10 hadoop_results/job2_user_activity.txt
-rw-r--r--. 1 <USER> <GROUP> 1.2K Dec 25 11:10 hadoop_results/job3_genre_preference.txt

本地文件内容摘要:
--- job1_movie_stats.txt ---
行数: 3706
文件大小: 229K
前3行内容:
1	评分总数: 2077, 平均评分: 4.15, 最高评分: 5.0, 最低评分: 1.0, 评分总和: 8619.00
2	评分总数: 701, 平均评分: 3.20, 最高评分: 5.0, 最低评分: 1.0, 评分总和: 2244.00
3	评分总数: 478, 平均评分: 3.02, 最高评分: 5.0, 最低评分: 1.0, 评分总和: 1443.00

--- job2_user_activity.txt ---
行数: 6040
文件大小: 121K
前3行内容:
1	评分次数: 53, 活跃度: 一般活跃
2	评分次数: 182, 活跃度: 非常活跃
3	评分次数: 27, 活跃度: 一般活跃

--- job3_genre_preference.txt ---
行数: 18
文件大小: 1.2K
前3行内容:
Film-Noir	评分总数: 18262, 平均评分: 4.08, 最高评分: 5.0, 最低评分: 1.0
Documentary	评分总数: 7912, 平均评分: 3.93, 最高评分: 5.0, 最低评分: 1.0
War	评分总数: 68531, 平均评分: 3.89, 最高评分: 5.0, 最低评分: 1.0
```

## 🔧 故障排除

### 常见问题及解决方案

#### 1. 编译错误
```bash
# 问题：找不到Hadoop类
# 解决：确保HADOOP_HOME和JAVA_HOME正确设置
export HADOOP_HOME=/path/to/hadoop
export JAVA_HOME=/path/to/java
export PATH=$HADOOP_HOME/bin:$HADOOP_HOME/sbin:$JAVA_HOME/bin:$PATH
```

#### 2. HDFS权限问题
```bash
# 问题：Permission denied
# 解决：检查HDFS权限
hdfs dfs -chmod 755 /input
hdfs dfs -chown $USER:$USER /input
```

#### 3. 内存不足
```bash
# 问题：Java heap space
# 解决：增加JVM内存
export HADOOP_OPTS="-Xmx2g"
```

#### 4. 任务失败
```bash
# 查看任务日志
yarn logs -applicationId application_xxx_xxx

# 查看JobHistory
mapred job -history /path/to/job/history
```

## 📊 性能优化建议

### 1. 调整MapReduce参数
```xml
<!-- 在mapred-site.xml中添加 -->
<property>
    <name>mapreduce.map.memory.mb</name>
    <value>2048</value>
</property>
<property>
    <name>mapreduce.reduce.memory.mb</name>
    <value>4096</value>
</property>
```

### 2. 优化HDFS块大小
```bash
# 设置较大的块大小以提高处理效率
hdfs dfs -D dfs.blocksize=268435456 -put large_file.dat /input/
```

### 3. 调整并行度
```bash
# 增加Reducer数量
hadoop jar target/movie-analysis.jar com.hadoop.movieanalysis.MovieAnalysisDriver /input /output all -D mapreduce.job.reduces=4
```

## 📈 监控和日志

### 1. Web界面监控
- **ResourceManager**: http://namenode:8088
- **NameNode**: http://namenode:9870
- **JobHistory**: http://namenode:19888

### 2. 命令行监控
```bash
# 查看正在运行的应用
yarn application -list

# 查看集群状态
yarn node -list

# 查看HDFS状态
hdfs dfsadmin -report
```

### 3. 日志查看
```bash
# 查看应用日志
yarn logs -applicationId application_xxx_xxx

# 查看特定容器日志
yarn logs -applicationId application_xxx_xxx -containerId container_xxx_xxx
```

## 🎉 验证部署成功

### 检查清单
- [ ] JAR文件编译成功
- [ ] 数据成功上传到HDFS
- [ ] MapReduce任务成功运行
- [ ] 输出结果正确生成
- [ ] 结果数据可以正常查看

### 预期结果
1. **任务1输出**: 每部电影的评分统计（总数、平均分、最值）
2. **任务2输出**: 每个用户的活跃度分级
3. **任务3输出**: 各电影类型的偏好排序

## 📝 部署脚本模板

创建 `deploy.sh` 一键部署脚本：
```bash
#!/bin/bash
echo "开始部署电影分析MapReduce项目..."

# 1. 编译项目
./compile_for_hadoop.sh

# 2. 清理HDFS
hdfs dfs -rm -r /input /output 2>/dev/null

# 3. 上传数据
hdfs dfs -mkdir /input
hdfs dfs -put input/*.dat /input/

# 4. 运行任务
hadoop jar target/movie-analysis.jar com.hadoop.movieanalysis.MovieAnalysisDriver /input /output all

# 5. 显示结果
echo "========================================="
echo "部署完成！查看结果："
echo "========================================="
hdfs dfs -cat /output/job1_movie_stats/part-r-00000 | head -10
echo "..."

echo "完整结果位置："
echo "- 电影评分统计: /output/job1_movie_stats/"
echo "- 用户活跃度分析: /output/job2_user_activity/"
echo "- 电影类型偏好: /output/job3_genre_preference/"
```

使用方法：
```bash
chmod +x deploy.sh
./deploy.sh
```

## 🔗 相关文档

- [Hadoop官方文档](https://hadoop.apache.org/docs/r3.1.3/)
- [MapReduce教程](https://hadoop.apache.org/docs/r3.1.3/hadoop-mapreduce-client/hadoop-mapreduce-client-core/MapReduceTutorial.html)
- [HDFS用户指南](https://hadoop.apache.org/docs/r3.1.3/hadoop-project-dist/hadoop-hdfs/HdfsUserGuide.html)
