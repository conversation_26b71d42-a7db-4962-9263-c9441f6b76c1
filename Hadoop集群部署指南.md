# Hadoop集群部署指南 - 电影分析MapReduce项目

## 🎯 部署概述

本指南提供了在Hadoop集群上部署电影分析MapReduce项目的完整步骤，**无需Maven环境**，适用于虚拟机或服务器环境。

## 📋 环境要求

- **Hadoop版本**: 3.1.3
- **Java版本**: 8 或更高版本
- **操作系统**: Linux/Unix（推荐）或Windows
- **内存**: 至少4GB可用内存
- **存储**: 至少10GB可用磁盘空间

## 🚀 快速部署步骤

### 步骤1：环境检查

```bash
# 检查Java环境
java -version
echo $JAVA_HOME

# 检查Hadoop环境
hadoop version
echo $HADOOP_HOME

# 检查HDFS状态
hdfs dfsadmin -report
```

### 步骤2：项目编译

#### Linux/Unix环境
```bash
# 使用提供的编译脚本
chmod +x compile_for_hadoop.sh
./compile_for_hadoop.sh
```

#### Windows环境
```cmd
# 使用批处理脚本
compile_for_hadoop.bat
```

#### 手动编译（通用方法）
```bash
# 1. 创建编译目录
mkdir -p target/classes

# 2. 设置Hadoop类路径
export HADOOP_CLASSPATH=$JAVA_HOME/lib/tools.jar

# 3. 按顺序编译各个包
hadoop com.sun.tools.javac.Main -d target/classes -sourcepath src/main/java src/main/java/com/hadoop/movieanalysis/model/*.java
hadoop com.sun.tools.javac.Main -d target/classes -sourcepath src/main/java src/main/java/com/hadoop/movieanalysis/writable/*.java
hadoop com.sun.tools.javac.Main -d target/classes -sourcepath src/main/java src/main/java/com/hadoop/movieanalysis/job1/*.java
hadoop com.sun.tools.javac.Main -d target/classes -sourcepath src/main/java src/main/java/com/hadoop/movieanalysis/job2/*.java
hadoop com.sun.tools.javac.Main -d target/classes -sourcepath src/main/java src/main/java/com/hadoop/movieanalysis/job3/*.java
hadoop com.sun.tools.javac.Main -d target/classes -sourcepath src/main/java src/main/java/com/hadoop/movieanalysis/*.java

# 4. 创建JAR文件
cd target/classes
jar cf ../movie-analysis.jar com/
cd ../..
```

### 步骤3：数据上传到HDFS

```bash
# 创建HDFS输入目录
hdfs dfs -mkdir /input

# 上传数据文件
hdfs dfs -put input/movies.dat /input/
hdfs dfs -put input/ratings.dat /input/
hdfs dfs -put input/users.dat /input/

# 验证上传
hdfs dfs -ls /input
hdfs dfs -du -h /input
```

### 步骤4：提交MapReduce任务

#### 运行所有任务
```bash
hadoop jar target/movie-analysis.jar com.hadoop.movieanalysis.MovieAnalysisDriver /input /output all
```

#### 运行单个任务
```bash
# 任务1：电影评分统计
hadoop jar target/movie-analysis.jar com.hadoop.movieanalysis.MovieAnalysisDriver /input /output 1

# 任务2：用户活跃度分析
hadoop jar target/movie-analysis.jar com.hadoop.movieanalysis.MovieAnalysisDriver /input /output 2

# 任务3：电影类型偏好分析
hadoop jar target/movie-analysis.jar com.hadoop.movieanalysis.MovieAnalysisDriver /input /output 3
```

### 步骤5：查看结果

```bash
# 查看输出目录结构
hdfs dfs -ls /output

# 查看电影评分统计结果（前20行）
hdfs dfs -cat /output/job1_movie_stats/part-r-00000 | head -20

# 查看用户活跃度分析结果（前20行）
hdfs dfs -cat /output/job2_user_activity/part-r-00000 | head -20

# 查看电影类型偏好分析结果
hdfs dfs -cat /output/job3_genre_preference/part-r-00000

# 下载结果到本地
hdfs dfs -get /output ./hadoop_output
```

## 🔧 故障排除

### 常见问题及解决方案

#### 1. 编译错误
```bash
# 问题：找不到Hadoop类
# 解决：确保HADOOP_HOME和JAVA_HOME正确设置
export HADOOP_HOME=/path/to/hadoop
export JAVA_HOME=/path/to/java
export PATH=$HADOOP_HOME/bin:$HADOOP_HOME/sbin:$JAVA_HOME/bin:$PATH
```

#### 2. HDFS权限问题
```bash
# 问题：Permission denied
# 解决：检查HDFS权限
hdfs dfs -chmod 755 /input
hdfs dfs -chown $USER:$USER /input
```

#### 3. 内存不足
```bash
# 问题：Java heap space
# 解决：增加JVM内存
export HADOOP_OPTS="-Xmx2g"
```

#### 4. 任务失败
```bash
# 查看任务日志
yarn logs -applicationId application_xxx_xxx

# 查看JobHistory
mapred job -history /path/to/job/history
```

## 📊 性能优化建议

### 1. 调整MapReduce参数
```xml
<!-- 在mapred-site.xml中添加 -->
<property>
    <name>mapreduce.map.memory.mb</name>
    <value>2048</value>
</property>
<property>
    <name>mapreduce.reduce.memory.mb</name>
    <value>4096</value>
</property>
```

### 2. 优化HDFS块大小
```bash
# 设置较大的块大小以提高处理效率
hdfs dfs -D dfs.blocksize=268435456 -put large_file.dat /input/
```

### 3. 调整并行度
```bash
# 增加Reducer数量
hadoop jar target/movie-analysis.jar com.hadoop.movieanalysis.MovieAnalysisDriver /input /output all -D mapreduce.job.reduces=4
```

## 📈 监控和日志

### 1. Web界面监控
- **ResourceManager**: http://namenode:8088
- **NameNode**: http://namenode:9870
- **JobHistory**: http://namenode:19888

### 2. 命令行监控
```bash
# 查看正在运行的应用
yarn application -list

# 查看集群状态
yarn node -list

# 查看HDFS状态
hdfs dfsadmin -report
```

### 3. 日志查看
```bash
# 查看应用日志
yarn logs -applicationId application_xxx_xxx

# 查看特定容器日志
yarn logs -applicationId application_xxx_xxx -containerId container_xxx_xxx
```

## 🎉 验证部署成功

### 检查清单
- [ ] JAR文件编译成功
- [ ] 数据成功上传到HDFS
- [ ] MapReduce任务成功运行
- [ ] 输出结果正确生成
- [ ] 结果数据可以正常查看

### 预期结果
1. **任务1输出**: 每部电影的评分统计（总数、平均分、最值）
2. **任务2输出**: 每个用户的活跃度分级
3. **任务3输出**: 各电影类型的偏好排序

## 📝 部署脚本模板

创建 `deploy.sh` 一键部署脚本：
```bash
#!/bin/bash
echo "开始部署电影分析MapReduce项目..."

# 1. 编译项目
./compile_for_hadoop.sh

# 2. 清理HDFS
hdfs dfs -rm -r /input /output 2>/dev/null

# 3. 上传数据
hdfs dfs -mkdir /input
hdfs dfs -put input/*.dat /input/

# 4. 运行任务
hadoop jar target/movie-analysis.jar com.hadoop.movieanalysis.MovieAnalysisDriver /input /output all

# 5. 显示结果
echo "========================================="
echo "部署完成！查看结果："
echo "========================================="
hdfs dfs -cat /output/job1_movie_stats/part-r-00000 | head -10
echo "..."

echo "完整结果位置："
echo "- 电影评分统计: /output/job1_movie_stats/"
echo "- 用户活跃度分析: /output/job2_user_activity/"
echo "- 电影类型偏好: /output/job3_genre_preference/"
```

使用方法：
```bash
chmod +x deploy.sh
./deploy.sh
```

## 🔗 相关文档

- [Hadoop官方文档](https://hadoop.apache.org/docs/r3.1.3/)
- [MapReduce教程](https://hadoop.apache.org/docs/r3.1.3/hadoop-mapreduce-client/hadoop-mapreduce-client-core/MapReduceTutorial.html)
- [HDFS用户指南](https://hadoop.apache.org/docs/r3.1.3/hadoop-project-dist/hadoop-hdfs/HdfsUserGuide.html)
