@echo off
REM 手动编译MapReduce项目
echo ========================================
echo 编译电影分析MapReduce项目
echo ========================================

REM 设置Java环境变量（根据您的Java安装路径调整）
set JAVA_HOME=C:\Program Files\Java\jdk-23
if not exist "%JAVA_HOME%" (
    echo 错误: 找不到Java安装目录 %JAVA_HOME%
    echo 请修改compile.bat中的JAVA_HOME路径
    pause
    exit /b 1
)

set PATH=%JAVA_HOME%\bin;%PATH%

REM 设置Hadoop环境变量（根据您的Hadoop安装路径调整）
set HADOOP_HOME=D:\hadoop\hadoop-3.1.3
if not exist "%HADOOP_HOME%" (
    echo 警告: 找不到Hadoop安装目录 %HADOOP_HOME%
    echo 将尝试使用项目中的Hadoop依赖
)

REM 设置项目路径
set PROJECT_DIR=%~dp0
set SRC_DIR=%PROJECT_DIR%src\main\java
set TARGET_DIR=%PROJECT_DIR%target\classes
set LIB_DIR=%PROJECT_DIR%lib

echo 项目目录: %PROJECT_DIR%
echo 源码目录: %SRC_DIR%
echo 输出目录: %TARGET_DIR%

REM 创建输出目录
if not exist "%TARGET_DIR%" mkdir "%TARGET_DIR%"
if not exist "%LIB_DIR%" mkdir "%LIB_DIR%"

REM 检查源码目录
if not exist "%SRC_DIR%" (
    echo 错误: 找不到源码目录 %SRC_DIR%
    pause
    exit /b 1
)

REM 设置类路径
set CLASSPATH=%TARGET_DIR%
if exist "%HADOOP_HOME%" (
    set CLASSPATH=%CLASSPATH%;%HADOOP_HOME%\share\hadoop\common\*
    set CLASSPATH=%CLASSPATH%;%HADOOP_HOME%\share\hadoop\common\lib\*
    set CLASSPATH=%CLASSPATH%;%HADOOP_HOME%\share\hadoop\hdfs\*
    set CLASSPATH=%CLASSPATH%;%HADOOP_HOME%\share\hadoop\hdfs\lib\*
    set CLASSPATH=%CLASSPATH%;%HADOOP_HOME%\share\hadoop\mapreduce\*
    set CLASSPATH=%CLASSPATH%;%HADOOP_HOME%\share\hadoop\mapreduce\lib\*
    set CLASSPATH=%CLASSPATH%;%HADOOP_HOME%\share\hadoop\yarn\*
    set CLASSPATH=%CLASSPATH%;%HADOOP_HOME%\share\hadoop\yarn\lib\*
)

echo 开始编译...

REM 编译所有Java文件
javac -cp "%CLASSPATH%" -d "%TARGET_DIR%" -sourcepath "%SRC_DIR%" "%SRC_DIR%\com\hadoop\movieanalysis\*.java" "%SRC_DIR%\com\hadoop\movieanalysis\model\*.java" "%SRC_DIR%\com\hadoop\movieanalysis\writable\*.java" "%SRC_DIR%\com\hadoop\movieanalysis\job1\*.java" "%SRC_DIR%\com\hadoop\movieanalysis\job2\*.java" "%SRC_DIR%\com\hadoop\movieanalysis\job3\*.java"

if %ERRORLEVEL% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo 编译成功！

REM 创建JAR文件
set JAR_FILE=%PROJECT_DIR%target\movie-analysis-mapreduce-1.0-SNAPSHOT.jar

echo 创建JAR文件...
cd /d "%TARGET_DIR%"
jar cf "%JAR_FILE%" com\

if %ERRORLEVEL% neq 0 (
    echo 创建JAR文件失败！
    pause
    exit /b 1
)

echo JAR文件创建成功: %JAR_FILE%

REM 返回项目目录
cd /d "%PROJECT_DIR%"

echo ========================================
echo 编译完成！
echo ========================================
echo JAR文件位置: %JAR_FILE%
echo 现在可以运行: run_local.bat
echo ========================================

pause
