package com.hadoop.movieanalysis.job1;

import com.hadoop.movieanalysis.writable.RatingStatistics;
import org.apache.hadoop.io.IntWritable;
import org.apache.hadoop.mapreduce.Reducer;

import java.io.IOException;

/**
 * 电影评分统计Combiner
 * 在Map端进行本地聚合，减少网络传输
 * 输入: Key=MovieID, Value=RatingStatistics
 * 输出: Key=MovieID, Value=RatingStatistics (合并后的)
 */
public class MovieRatingStatsCombiner extends Reducer<IntWritable, RatingStatistics, IntWritable, RatingStatistics> {

    private RatingStatistics result = new RatingStatistics();

    @Override
    protected void reduce(IntWritable key, Iterable<RatingStatistics> values, Context context)
            throws IOException, InterruptedException {

        // 重置结果对象
        result = new RatingStatistics();

        // 合并所有统计信息
        for (RatingStatistics stats : values) {
            result.merge(stats);
        }

        // 输出合并后的统计信息
        context.write(key, result);
        
        // 更新计数器
        context.getCounter("MovieRatingStatsCombiner", "MOVIES_PROCESSED").increment(1);
    }
}
