package com.hadoop.movieanalysis.model;

import org.apache.hadoop.io.Writable;
import java.io.DataInput;
import java.io.DataOutput;
import java.io.IOException;

/**
 * 用户数据模型
 * 格式: UserID::Gender::Age::Occupation::Zip-code
 */
public class User implements Writable {
    private int userId;
    private String gender;
    private int age;
    private int occupation;
    private String zipCode;

    public User() {
        // 默认构造函数
    }

    public User(int userId, String gender, int age, int occupation, String zipCode) {
        this.userId = userId;
        this.gender = gender;
        this.age = age;
        this.occupation = occupation;
        this.zipCode = zipCode;
    }

    /**
     * 从字符串解析用户信息
     * @param line 输入行，格式: UserID::Gender::Age::Occupation::Zip-code
     * @return User对象
     */
    public static User parseFromString(String line) {
        String[] parts = line.split("::");
        if (parts.length >= 5) {
            int userId = Integer.parseInt(parts[0]);
            String gender = parts[1];
            int age = Integer.parseInt(parts[2]);
            int occupation = Integer.parseInt(parts[3]);
            String zipCode = parts[4];
            return new User(userId, gender, age, occupation, zipCode);
        }
        return null;
    }

    @Override
    public void write(DataOutput out) throws IOException {
        out.writeInt(userId);
        out.writeUTF(gender);
        out.writeInt(age);
        out.writeInt(occupation);
        out.writeUTF(zipCode);
    }

    @Override
    public void readFields(DataInput in) throws IOException {
        userId = in.readInt();
        gender = in.readUTF();
        age = in.readInt();
        occupation = in.readInt();
        zipCode = in.readUTF();
    }

    // Getters and Setters
    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public int getAge() {
        return age;
    }

    public void setAge(int age) {
        this.age = age;
    }

    public int getOccupation() {
        return occupation;
    }

    public void setOccupation(int occupation) {
        this.occupation = occupation;
    }

    public String getZipCode() {
        return zipCode;
    }

    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

    @Override
    public String toString() {
        return userId + "::" + gender + "::" + age + "::" + occupation + "::" + zipCode;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        User user = (User) obj;
        return userId == user.userId;
    }

    @Override
    public int hashCode() {
        return Integer.hashCode(userId);
    }
}
