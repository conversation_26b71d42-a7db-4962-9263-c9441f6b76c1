#!/bin/bash
# 电影分析MapReduce项目一键部署脚本
# 适用于Hadoop集群环境（无Maven）

echo "========================================="
echo "电影分析MapReduce项目一键部署"
echo "电影网站用户影评分析 - MapReduce高阶编程"
echo "========================================="

# 检查环境
echo "1. 检查环境..."
if [ -z "$HADOOP_HOME" ]; then
    echo "错误: HADOOP_HOME环境变量未设置"
    exit 1
fi

if [ -z "$JAVA_HOME" ]; then
    echo "错误: JAVA_HOME环境变量未设置"
    exit 1
fi

echo "✓ Hadoop环境: $HADOOP_HOME"
echo "✓ Java环境: $JAVA_HOME"

# 检查Hadoop服务状态
echo "2. 检查Hadoop服务状态..."
if ! hadoop version >/dev/null 2>&1; then
    echo "错误: Hadoop服务未启动或配置有误"
    exit 1
fi

if ! hdfs dfs -ls / >/dev/null 2>&1; then
    echo "错误: HDFS服务未启动或无法访问"
    exit 1
fi

echo "✓ Hadoop服务正常"
echo "✓ HDFS服务正常"

# 编译项目
echo "3. 编译MapReduce项目..."
if [ -f "compile_for_hadoop.sh" ]; then
    chmod +x compile_for_hadoop.sh
    ./compile_for_hadoop.sh
    if [ $? -ne 0 ]; then
        echo "错误: 项目编译失败"
        exit 1
    fi
else
    echo "错误: 找不到编译脚本 compile_for_hadoop.sh"
    exit 1
fi

echo "✓ 项目编译成功"

# 检查JAR文件
if [ ! -f "target/movie-analysis.jar" ]; then
    echo "错误: JAR文件不存在"
    exit 1
fi

echo "✓ JAR文件准备就绪: target/movie-analysis.jar"

# 清理HDFS旧数据
echo "4. 清理HDFS旧数据..."
hdfs dfs -rm -r /input /output 2>/dev/null
echo "✓ HDFS清理完成"

# 创建HDFS目录并上传数据
echo "5. 上传数据到HDFS..."
hdfs dfs -mkdir /input
if [ $? -ne 0 ]; then
    echo "错误: 无法创建HDFS输入目录"
    exit 1
fi

# 检查本地数据文件
for file in movies.dat ratings.dat users.dat; do
    if [ ! -f "input/$file" ]; then
        echo "错误: 找不到数据文件 input/$file"
        exit 1
    fi
done

# 上传数据文件
hdfs dfs -put input/movies.dat /input/
hdfs dfs -put input/ratings.dat /input/
hdfs dfs -put input/users.dat /input/

if [ $? -eq 0 ]; then
    echo "✓ 数据上传成功"
else
    echo "错误: 数据上传失败"
    exit 1
fi

# 验证上传的数据
echo "6. 验证上传的数据..."
echo "HDFS输入目录内容:"
hdfs dfs -ls /input
echo "数据文件大小:"
hdfs dfs -du -h /input

# 提交MapReduce任务
echo "7. 提交MapReduce任务..."
echo "开始运行电影分析任务..."

start_time=$(date +%s)

hadoop jar target/movie-analysis.jar com.hadoop.movieanalysis.MovieAnalysisDriver /input /output all

if [ $? -eq 0 ]; then
    end_time=$(date +%s)
    duration=$((end_time - start_time))
    echo "✓ MapReduce任务执行成功"
    echo "✓ 执行时间: ${duration}秒"
else
    echo "错误: MapReduce任务执行失败"
    echo "请检查日志获取详细错误信息"
    exit 1
fi

# 验证输出结果
echo "8. 验证输出结果..."
echo "HDFS输出目录结构:"
hdfs dfs -ls /output

# 检查各个任务的输出
for job in job1_movie_stats job2_user_activity job3_genre_preference; do
    if hdfs dfs -test -e /output/$job/part-r-00000; then
        echo "✓ $job 输出文件存在"
    else
        echo "✗ $job 输出文件不存在"
    fi
done

# 显示部分结果
echo "9. 显示分析结果..."
echo
echo "========================================="
echo "【任务1】电影评分统计分析结果（前10行）:"
echo "========================================="
hdfs dfs -cat /output/job1_movie_stats/part-r-00000 | head -10

echo
echo "========================================="
echo "【任务2】用户活跃度分析结果（前10行）:"
echo "========================================="
hdfs dfs -cat /output/job2_user_activity/part-r-00000 | head -10

echo
echo "========================================="
echo "【任务3】电影类型偏好分析结果:"
echo "========================================="
hdfs dfs -cat /output/job3_genre_preference/part-r-00000

# 生成统计摘要
echo
echo "========================================="
echo "统计摘要:"
echo "========================================="

# 统计处理的数据量
movie_count=$(hdfs dfs -cat /output/job1_movie_stats/part-r-00000 | wc -l)
user_count=$(hdfs dfs -cat /output/job2_user_activity/part-r-00000 | wc -l)
genre_count=$(hdfs dfs -cat /output/job3_genre_preference/part-r-00000 | wc -l)

echo "✓ 处理电影数量: $movie_count 部"
echo "✓ 分析用户数量: $user_count 个"
echo "✓ 电影类型数量: $genre_count 种"
echo "✓ 原始评分记录: 1,000,209 条"

# 下载结果到本地（可选）
echo
echo "10. 下载结果到本地..."
rm -rf hadoop_output 2>/dev/null
hdfs dfs -get /output hadoop_output
if [ $? -eq 0 ]; then
    echo "✓ 结果已下载到本地目录: hadoop_output/"
else
    echo "✗ 结果下载失败"
fi

echo
echo "========================================="
echo "部署完成！"
echo "========================================="
echo "项目特点:"
echo "✓ MapReduce高阶编程技术"
echo "✓ Combiner优化 - 减少网络传输"
echo "✓ Partitioner优化 - 数据均匀分布"
echo "✓ 自定义WritableComparable - 复合键排序"
echo "✓ 求最值、求和、计数等统计功能"
echo "✓ 大数据处理和分析"
echo
echo "结果位置:"
echo "- HDFS: /output/"
echo "- 本地: hadoop_output/"
echo
echo "查看完整结果:"
echo "hdfs dfs -cat /output/job1_movie_stats/part-r-00000"
echo "hdfs dfs -cat /output/job2_user_activity/part-r-00000"
echo "hdfs dfs -cat /output/job3_genre_preference/part-r-00000"
echo
echo "========================================="
