# 电影分析MapReduce项目 - 完整部署教程

## 🎯 教程说明

这是一份**最完整、最详细**的Hadoop集群非本地部署运行教程。每一步都包含具体的路径、命令和预期输出，确保您能够成功完成部署。

**适用环境**: Hadoop 3.1.3 + Java 8+ + Linux/Unix系统
**无需Maven**: 本教程不依赖Maven环境

## 📋 前置条件检查

在开始之前，请确保：
- ✅ Hadoop集群已正常运行
- ✅ HDFS和YARN服务正常
- ✅ 您有Hadoop操作权限
- ✅ 项目源代码已准备好

## 🚀 完整部署流程

### 阶段一：环境准备 (5分钟)

#### 1.1 登录集群并检查环境

```bash
# 登录到Hadoop集群主节点
ssh hadoop@your-namenode-ip

# 检查当前位置和用户
pwd && whoami
# 预期输出: /home/<USER>

# 检查Java环境
java -version
echo "JAVA_HOME: $JAVA_HOME"

# 检查Hadoop环境
hadoop version | head -3
echo "HADOOP_HOME: $HADOOP_HOME"

# 检查集群状态
hdfs dfsadmin -report | head -10
yarn node -list
jps
```

#### 1.2 创建项目工作目录

```bash
# 创建项目目录
cd ~
mkdir -p movie-analysis-project
cd movie-analysis-project
pwd
# 预期输出: /home/<USER>/movie-analysis-project
```

### 阶段二：项目上传 (10分钟)

#### 2.1 上传项目文件

**方法A: 使用scp上传（推荐）**
```bash
# 在本地机器执行（替换IP地址）
scp -r /path/to/mjyhadoop/* hadoop@*************:/home/<USER>/movie-analysis-project/
```

**方法B: 手动创建项目结构**
```bash
# 在集群上创建目录结构
mkdir -p src/main/java/com/hadoop/movieanalysis/{model,writable,job1,job2,job3}
mkdir -p input target/classes

# 然后手动复制Java源文件和数据文件到相应目录
```

#### 2.2 验证项目文件

```bash
cd /home/<USER>/movie-analysis-project

# 检查项目结构
find . -name "*.java" | wc -l
# 预期输出: 应该有15+个Java文件

find . -name "*.dat"
# 预期输出: ./input/movies.dat ./input/ratings.dat ./input/users.dat

# 检查数据文件
ls -lh input/
wc -l input/*.dat
# 预期输出: movies.dat约3883行, ratings.dat约1000209行, users.dat约6040行
```

### 阶段三：编译项目 (15分钟)

#### 3.1 设置编译环境

```bash
cd /home/<USER>/movie-analysis-project
export HADOOP_CLASSPATH=$JAVA_HOME/lib/tools.jar
mkdir -p target/classes
```

#### 3.2 按顺序编译各个包

```bash
# 编译数据模型
echo "编译数据模型..."
hadoop com.sun.tools.javac.Main -d target/classes -sourcepath src/main/java src/main/java/com/hadoop/movieanalysis/model/*.java
ls target/classes/com/hadoop/movieanalysis/model/
# 预期输出: Movie.class Rating.class User.class

# 编译Writable类
echo "编译Writable类..."
hadoop com.sun.tools.javac.Main -d target/classes -sourcepath src/main/java src/main/java/com/hadoop/movieanalysis/writable/*.java

# 编译MapReduce任务
echo "编译MapReduce任务..."
hadoop com.sun.tools.javac.Main -d target/classes -sourcepath src/main/java src/main/java/com/hadoop/movieanalysis/job1/*.java
hadoop com.sun.tools.javac.Main -d target/classes -sourcepath src/main/java src/main/java/com/hadoop/movieanalysis/job2/*.java
hadoop com.sun.tools.javac.Main -d target/classes -sourcepath src/main/java src/main/java/com/hadoop/movieanalysis/job3/*.java

# 编译主驱动程序
echo "编译主驱动程序..."
hadoop com.sun.tools.javac.Main -d target/classes -sourcepath src/main/java src/main/java/com/hadoop/movieanalysis/*.java
```

#### 3.3 创建JAR文件

```bash
cd target/classes
jar cf ../movie-analysis.jar com/
cd ../..

# 验证JAR文件
ls -lh target/movie-analysis.jar
jar tf target/movie-analysis.jar | head -5
# 预期输出: 显示JAR文件大小和内容
```

### 阶段四：数据上传到HDFS (10分钟)

#### 4.1 清理和准备HDFS

```bash
# 清理旧数据
hdfs dfs -rm -r /input /output 2>/dev/null || echo "目录不存在，跳过"

# 创建输入目录
hdfs dfs -mkdir /input
hdfs dfs -ls /
# 预期输出: 应该看到/input目录
```

#### 4.2 上传数据文件

```bash
# 上传数据文件
echo "上传movies.dat..."
hdfs dfs -put input/movies.dat /input/

echo "上传ratings.dat..."
hdfs dfs -put input/ratings.dat /input/

echo "上传users.dat..."
hdfs dfs -put input/users.dat /input/

# 验证上传
hdfs dfs -ls /input
hdfs dfs -du -h /input
# 预期输出: 3个文件，总大小约24MB
```

#### 4.3 验证HDFS数据

```bash
# 检查文件内容
hdfs dfs -cat /input/movies.dat | head -3
hdfs dfs -cat /input/ratings.dat | head -3
hdfs dfs -cat /input/users.dat | head -3

# 统计行数
echo "HDFS数据统计:"
echo "movies.dat: $(hdfs dfs -cat /input/movies.dat | wc -l) 行"
echo "ratings.dat: $(hdfs dfs -cat /input/ratings.dat | wc -l) 行"
echo "users.dat: $(hdfs dfs -cat /input/users.dat | wc -l) 行"
```

### 阶段五：提交MapReduce任务 (20分钟)

#### 5.1 检查集群状态

```bash
# 检查YARN状态
yarn node -list
# 预期输出: 显示所有节点为RUNNING状态

# 检查资源使用情况
yarn top
```

#### 5.2 提交任务

```bash
cd /home/<USER>/movie-analysis-project

# 记录开始时间
echo "开始时间: $(date)"
start_time=$(date +%s)

# 提交所有MapReduce任务
echo "========================================="
echo "提交电影分析MapReduce任务到Hadoop集群"
echo "========================================="

hadoop jar target/movie-analysis.jar com.hadoop.movieanalysis.MovieAnalysisDriver /input /output all

# 记录结束时间
end_time=$(date +%s)
duration=$((end_time - start_time))
echo "结束时间: $(date)"
echo "总执行时间: ${duration}秒"
```

#### 5.3 监控任务执行

在任务执行过程中，您可以：

```bash
# 在另一个终端查看任务状态
yarn application -list

# 查看Web界面
echo "ResourceManager Web UI: http://your-namenode:8088"
echo "NameNode Web UI: http://your-namenode:9870"
```

### 阶段六：查看结果 (10分钟)

#### 6.1 验证输出

```bash
# 检查输出目录
hdfs dfs -ls /output
# 预期输出: 3个子目录 job1_movie_stats, job2_user_activity, job3_genre_preference

# 检查各任务输出
for job in job1_movie_stats job2_user_activity job3_genre_preference; do
    echo "=== $job ==="
    hdfs dfs -ls /output/$job
    hdfs dfs -du -h /output/$job/part-r-00000
done
```

#### 6.2 查看分析结果

```bash
# 任务1: 电影评分统计（前10行）
echo "========== 任务1: 电影评分统计 =========="
hdfs dfs -cat /output/job1_movie_stats/part-r-00000 | head -10

# 任务2: 用户活跃度分析（前10行）
echo "========== 任务2: 用户活跃度分析 =========="
hdfs dfs -cat /output/job2_user_activity/part-r-00000 | head -10

# 任务3: 电影类型偏好分析（完整结果）
echo "========== 任务3: 电影类型偏好分析 =========="
hdfs dfs -cat /output/job3_genre_preference/part-r-00000
```

#### 6.3 下载结果到本地

```bash
# 创建本地结果目录
mkdir -p hadoop_results

# 下载所有结果
hdfs dfs -get /output/job1_movie_stats/part-r-00000 hadoop_results/job1_movie_stats.txt
hdfs dfs -get /output/job2_user_activity/part-r-00000 hadoop_results/job2_user_activity.txt
hdfs dfs -get /output/job3_genre_preference/part-r-00000 hadoop_results/job3_genre_preference.txt

# 验证下载
ls -lh hadoop_results/
```

### 阶段七：结果分析 (5分钟)

#### 7.1 生成统计报告

```bash
cd /home/<USER>/movie-analysis-project

echo "========================================="
echo "电影分析MapReduce项目执行报告"
echo "========================================="
echo "执行时间: $(date)"
echo "集群信息: $(hostname)"
echo

echo "数据处理统计:"
echo "- 处理电影数量: $(wc -l < hadoop_results/job1_movie_stats.txt)"
echo "- 分析用户数量: $(wc -l < hadoop_results/job2_user_activity.txt)"
echo "- 电影类型数量: $(wc -l < hadoop_results/job3_genre_preference.txt)"
echo "- 原始评分记录: 1,000,209 条"

echo
echo "用户活跃度分布:"
echo "- 非常活跃用户: $(grep '非常活跃' hadoop_results/job2_user_activity.txt | wc -l)"
echo "- 活跃用户: $(grep -w '活跃' hadoop_results/job2_user_activity.txt | wc -l)"
echo "- 一般活跃用户: $(grep '一般活跃' hadoop_results/job2_user_activity.txt | wc -l)"
echo "- 不活跃用户: $(grep '不活跃' hadoop_results/job2_user_activity.txt | wc -l)"

echo
echo "最受欢迎的电影类型（前5名）:"
head -5 hadoop_results/job3_genre_preference.txt | awk -F'\t' '{print "- " $1}'

echo
echo "========================================="
echo "部署完成！所有任务执行成功！"
echo "========================================="
```

## ✅ 验证清单

请确认以下所有项目都已完成：

- [ ] Hadoop集群环境正常
- [ ] 项目文件上传成功
- [ ] Java源代码编译成功
- [ ] JAR文件创建成功
- [ ] 数据上传到HDFS成功
- [ ] MapReduce任务执行成功
- [ ] 三个分析任务都有输出结果
- [ ] 结果文件下载到本地成功
- [ ] 结果数据格式正确

## 🎯 预期结果

成功完成后，您应该得到：

1. **任务1输出**: 3,706部电影的评分统计
2. **任务2输出**: 6,040个用户的活跃度分析
3. **任务3输出**: 18种电影类型的偏好排序

## 📞 技术支持

如果遇到问题，请检查：
1. Hadoop集群状态: `jps` 和 `yarn node -list`
2. HDFS状态: `hdfs dfsadmin -report`
3. 任务日志: `yarn logs -applicationId <app_id>`
4. 错误日志: 查看Hadoop日志目录

---

**恭喜！您已成功完成电影分析MapReduce项目的Hadoop集群部署！**
