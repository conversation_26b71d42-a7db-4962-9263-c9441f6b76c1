package com.hadoop.movieanalysis.simple;

/**
 * 简化的电影数据模型（不依赖Hadoop）
 */
public class SimpleMovie {
    private int movieId;
    private String title;
    private String genres;

    public SimpleMovie() {
        // 默认构造函数
    }

    public SimpleMovie(int movieId, String title, String genres) {
        this.movieId = movieId;
        this.title = title;
        this.genres = genres;
    }

    /**
     * 从字符串解析电影信息
     * @param line 输入行，格式: MovieID::Title::Genres
     * @return SimpleMovie对象
     */
    public static SimpleMovie parseFromString(String line) {
        String[] parts = line.split("::");
        if (parts.length >= 3) {
            int movieId = Integer.parseInt(parts[0]);
            String title = parts[1];
            String genres = parts[2];
            return new SimpleMovie(movieId, title, genres);
        }
        return null;
    }

    // Getters and Setters
    public int getMovieId() {
        return movieId;
    }

    public void setMovieId(int movieId) {
        this.movieId = movieId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getGenres() {
        return genres;
    }

    public void setGenres(String genres) {
        this.genres = genres;
    }

    @Override
    public String toString() {
        return movieId + "::" + title + "::" + genres;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        SimpleMovie movie = (SimpleMovie) obj;
        return movieId == movie.movieId;
    }

    @Override
    public int hashCode() {
        return Integer.hashCode(movieId);
    }
}
