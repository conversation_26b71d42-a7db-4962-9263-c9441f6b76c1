package com.hadoop.movieanalysis.job3;

import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Reducer;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 电影类型偏好分析Reducer
 * 输入: Key=DataType, Value=数据内容
 * 输出: Key=Genre, Value=统计信息
 */
public class GenrePreferenceReducer extends Reducer<Text, Text, Text, Text> {

    private Text genreKey = new Text();
    private Text result = new Text();
    
    // 存储电影ID到类型的映射
    private Map<Integer, String> movieGenres = new HashMap<>();
    // 存储类型统计信息
    private Map<String, GenreStats> genreStats = new HashMap<>();

    @Override
    protected void reduce(Text key, Iterable<Text> values, Context context)
            throws IOException, InterruptedException {

        String keyStr = key.toString();
        
        if ("MOVIE_DATA".equals(keyStr)) {
            // 处理电影数据，建立电影ID到类型的映射
            for (Text value : values) {
                String line = value.toString();
                if (line.startsWith("MOVIE:")) {
                    String[] parts = line.substring(6).split(":");
                    if (parts.length >= 2) {
                        int movieId = Integer.parseInt(parts[0]);
                        String genres = parts[1];
                        movieGenres.put(movieId, genres);
                    }
                }
            }
        } else if ("RATING_DATA".equals(keyStr)) {
            // 处理评分数据
            for (Text value : values) {
                String line = value.toString();
                if (line.startsWith("RATING:")) {
                    String[] parts = line.substring(7).split(":");
                    if (parts.length >= 2) {
                        int movieId = Integer.parseInt(parts[0]);
                        double rating = Double.parseDouble(parts[1]);
                        
                        // 获取电影类型
                        String genres = movieGenres.get(movieId);
                        if (genres != null) {
                            // 处理多个类型（用|分隔）
                            String[] genreArray = genres.split("\\|");
                            for (String genre : genreArray) {
                                genre = genre.trim();
                                if (!genre.isEmpty()) {
                                    GenreStats stats = genreStats.getOrDefault(genre, new GenreStats());
                                    stats.addRating(rating);
                                    genreStats.put(genre, stats);
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    @Override
    protected void cleanup(Context context) throws IOException, InterruptedException {
        // 在cleanup阶段输出所有类型的统计信息
        for (Map.Entry<String, GenreStats> entry : genreStats.entrySet()) {
            String genre = entry.getKey();
            GenreStats stats = entry.getValue();
            
            String output = String.format(
                "评分总数: %d, 平均评分: %.2f, 最高评分: %.1f, 最低评分: %.1f",
                stats.getCount(),
                stats.getAverageRating(),
                stats.getMaxRating(),
                stats.getMinRating()
            );
            
            genreKey.set(genre);
            result.set(output);
            context.write(genreKey, result);
            
            // 更新计数器
            context.getCounter("GenrePreferenceReducer", "GENRES_PROCESSED").increment(1);
        }
    }

    /**
     * 类型统计信息内部类
     */
    private static class GenreStats {
        private long count = 0;
        private double sum = 0.0;
        private double maxRating = Double.MIN_VALUE;
        private double minRating = Double.MAX_VALUE;

        public void addRating(double rating) {
            count++;
            sum += rating;
            maxRating = Math.max(maxRating, rating);
            minRating = Math.min(minRating, rating);
        }

        public long getCount() { return count; }
        public double getSum() { return sum; }
        public double getMaxRating() { return maxRating; }
        public double getMinRating() { return minRating; }
        public double getAverageRating() { return count > 0 ? sum / count : 0.0; }
    }
}
