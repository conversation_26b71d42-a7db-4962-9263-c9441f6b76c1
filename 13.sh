#!/bin/bash
# 电影分析MapReduce项目 - 一键部署脚本
# 适用于Hadoop 3.1.3集群环境（无Maven）
# 作者: MapReduce高阶编程项目
# 版本: 1.0

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 命令不存在，请检查环境配置"
        exit 1
    fi
}

# 检查环境变量
check_env() {
    if [ -z "${!1}" ]; then
        log_error "环境变量 $1 未设置"
        exit 1
    fi
}

echo "========================================="
echo "电影分析MapReduce项目 - 一键部署脚本"
echo "Hadoop集群非本地部署（无Maven环境）"
echo "========================================="

# 记录开始时间
SCRIPT_START_TIME=$(date +%s)
log_info "部署开始时间: $(date)"

# 阶段1: 环境检查
log_info "阶段1: 环境检查..."

# 检查必要命令
check_command "java"
check_command "hadoop"
check_command "hdfs"
check_command "yarn"
check_command "jar"

# 检查环境变量
check_env "JAVA_HOME"
check_env "HADOOP_HOME"

# 检查Java版本
JAVA_VERSION=$(java -version 2>&1 | head -1 | cut -d'"' -f2)
log_info "Java版本: $JAVA_VERSION"

# 检查Hadoop版本
HADOOP_VERSION=$(hadoop version | head -1)
log_info "Hadoop版本: $HADOOP_VERSION"

# 检查集群状态
if ! hdfs dfs -ls / &>/dev/null; then
    log_error "HDFS服务不可用，请检查集群状态"
    exit 1
fi

if ! yarn node -list &>/dev/null; then
    log_error "YARN服务不可用，请检查集群状态"
    exit 1
fi

log_success "环境检查完成"

# 阶段2: 项目准备
log_info "阶段2: 项目准备..."

# 设置项目目录
PROJECT_DIR="$HOME/movie-analysis-project"
log_info "项目目录: $PROJECT_DIR"

# 检查项目目录是否存在
if [ ! -d "$PROJECT_DIR" ]; then
    log_error "项目目录不存在: $PROJECT_DIR"
    log_info "请先上传项目文件到该目录"
    exit 1
fi

cd "$PROJECT_DIR"

# 检查必要文件
REQUIRED_FILES=(
    "src/main/java/com/hadoop/movieanalysis/MovieAnalysisDriver.java"
    "input/movies.dat"
    "input/ratings.dat"
    "input/users.dat"
)

for file in "${REQUIRED_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        log_error "必要文件不存在: $file"
        exit 1
    fi
done

# 统计项目文件
JAVA_FILES=$(find src -name "*.java" | wc -l)
log_info "Java源文件数量: $JAVA_FILES"

# 检查数据文件
log_info "数据文件统计:"
for file in input/*.dat; do
    lines=$(wc -l < "$file")
    size=$(ls -lh "$file" | awk '{print $5}')
    log_info "  $(basename $file): $lines 行, $size"
done

log_success "项目准备完成"

# 阶段3: 编译项目
log_info "阶段3: 编译项目..."

# 设置编译环境
export HADOOP_CLASSPATH=$JAVA_HOME/lib/tools.jar
mkdir -p target/classes

# 编译各个包
log_info "编译数据模型包..."
hadoop com.sun.tools.javac.Main -d target/classes -sourcepath src/main/java src/main/java/com/hadoop/movieanalysis/model/*.java

log_info "编译Writable类..."
hadoop com.sun.tools.javac.Main -d target/classes -sourcepath src/main/java src/main/java/com/hadoop/movieanalysis/writable/*.java

log_info "编译MapReduce任务..."
hadoop com.sun.tools.javac.Main -d target/classes -sourcepath src/main/java src/main/java/com/hadoop/movieanalysis/job1/*.java
hadoop com.sun.tools.javac.Main -d target/classes -sourcepath src/main/java src/main/java/com/hadoop/movieanalysis/job2/*.java
hadoop com.sun.tools.javac.Main -d target/classes -sourcepath src/main/java src/main/java/com/hadoop/movieanalysis/job3/*.java

log_info "编译主驱动程序..."
hadoop com.sun.tools.javac.Main -d target/classes -sourcepath src/main/java src/main/java/com/hadoop/movieanalysis/*.java

# 创建JAR文件
log_info "创建JAR文件..."
cd target/classes
jar cf ../movie-analysis.jar com/
cd ../..

# 验证JAR文件
if [ ! -f "target/movie-analysis.jar" ]; then
    log_error "JAR文件创建失败"
    exit 1
fi

JAR_SIZE=$(ls -lh target/movie-analysis.jar | awk '{print $5}')
log_success "编译完成，JAR文件大小: $JAR_SIZE"

# 阶段4: 数据上传到HDFS
log_info "阶段4: 数据上传到HDFS..."

# 清理旧数据
log_info "清理HDFS旧数据..."
hdfs dfs -rm -r /input /output 2>/dev/null || log_warning "旧数据不存在，跳过清理"

# 创建输入目录
log_info "创建HDFS输入目录..."
hdfs dfs -mkdir /input

# 上传数据文件
log_info "上传数据文件到HDFS..."
hdfs dfs -put input/movies.dat /input/
hdfs dfs -put input/ratings.dat /input/
hdfs dfs -put input/users.dat /input/

# 验证上传
HDFS_FILES=$(hdfs dfs -ls /input | grep -c "\.dat")
if [ "$HDFS_FILES" -ne 3 ]; then
    log_error "数据文件上传不完整"
    exit 1
fi

log_success "数据上传完成"

# 阶段5: 提交MapReduce任务
log_info "阶段5: 提交MapReduce任务..."

# 检查集群资源
ACTIVE_NODES=$(yarn node -list | grep RUNNING | wc -l)
log_info "活跃节点数: $ACTIVE_NODES"

# 提交任务
log_info "提交电影分析MapReduce任务..."
JOB_START_TIME=$(date +%s)

hadoop jar target/movie-analysis.jar com.hadoop.movieanalysis.MovieAnalysisDriver /input /output all

JOB_END_TIME=$(date +%s)
JOB_DURATION=$((JOB_END_TIME - JOB_START_TIME))

log_success "MapReduce任务执行完成，耗时: ${JOB_DURATION}秒"

# 阶段6: 验证结果
log_info "阶段6: 验证结果..."

# 检查输出目录
OUTPUT_DIRS=$(hdfs dfs -ls /output | grep "job" | wc -l)
if [ "$OUTPUT_DIRS" -ne 3 ]; then
    log_error "输出目录不完整，预期3个，实际$OUTPUT_DIRS个"
    exit 1
fi

# 检查输出文件
for job in job1_movie_stats job2_user_activity job3_genre_preference; do
    if hdfs dfs -test -e /output/$job/part-r-00000; then
        file_size=$(hdfs dfs -du -h /output/$job/part-r-00000 | awk '{print $1}')
        log_success "$job 输出文件存在，大小: $file_size"
    else
        log_error "$job 输出文件不存在"
        exit 1
    fi
done

# 阶段7: 下载结果
log_info "阶段7: 下载结果到本地..."

mkdir -p hadoop_results

hdfs dfs -get /output/job1_movie_stats/part-r-00000 hadoop_results/job1_movie_stats.txt
hdfs dfs -get /output/job2_user_activity/part-r-00000 hadoop_results/job2_user_activity.txt
hdfs dfs -get /output/job3_genre_preference/part-r-00000 hadoop_results/job3_genre_preference.txt

log_success "结果下载完成"

# 阶段8: 生成报告
log_info "阶段8: 生成分析报告..."

REPORT_FILE="deployment_report.txt"

cat > "$REPORT_FILE" << EOF
========================================
电影分析MapReduce项目部署报告
========================================
部署时间: $(date)
集群主机: $(hostname)
用户: $(whoami)
项目目录: $PROJECT_DIR

环境信息:
- Java版本: $JAVA_VERSION
- Hadoop版本: $HADOOP_VERSION
- 活跃节点数: $ACTIVE_NODES

数据处理统计:
- 处理电影数量: $(wc -l < hadoop_results/job1_movie_stats.txt)
- 分析用户数量: $(wc -l < hadoop_results/job2_user_activity.txt)
- 电影类型数量: $(wc -l < hadoop_results/job3_genre_preference.txt)
- 原始评分记录: 1,000,209 条

用户活跃度分布:
- 非常活跃用户: $(grep '非常活跃' hadoop_results/job2_user_activity.txt | wc -l)
- 活跃用户: $(grep -w '活跃' hadoop_results/job2_user_activity.txt | wc -l)
- 一般活跃用户: $(grep '一般活跃' hadoop_results/job2_user_activity.txt | wc -l)
- 不活跃用户: $(grep '不活跃' hadoop_results/job2_user_activity.txt | wc -l)

性能统计:
- MapReduce任务执行时间: ${JOB_DURATION}秒
- 总部署时间: $(($(date +%s) - SCRIPT_START_TIME))秒

技术特点:
✓ MapReduce高阶编程技术
✓ Combiner优化 - 减少网络传输
✓ Partitioner优化 - 数据均匀分布
✓ 自定义WritableComparable - 复合键排序
✓ 求最值、求和、计数等统计功能
✓ 大数据处理和分析

结果文件位置:
- 本地: $PROJECT_DIR/hadoop_results/
- HDFS: /output/

========================================
部署成功完成！
========================================
EOF

log_success "分析报告已生成: $REPORT_FILE"

# 显示部分结果
echo
echo "========================================="
echo "部署结果预览"
echo "========================================="

echo "【任务1】电影评分统计（前5行）:"
head -5 hadoop_results/job1_movie_stats.txt

echo
echo "【任务2】用户活跃度分析（前5行）:"
head -5 hadoop_results/job2_user_activity.txt

echo
echo "【任务3】电影类型偏好分析:"
cat hadoop_results/job3_genre_preference.txt

# 计算总执行时间
SCRIPT_END_TIME=$(date +%s)
TOTAL_DURATION=$((SCRIPT_END_TIME - SCRIPT_START_TIME))

echo
echo "========================================="
log_success "部署完成！"
echo "========================================="
log_info "总执行时间: ${TOTAL_DURATION}秒"
log_info "MapReduce任务时间: ${JOB_DURATION}秒"
log_info "部署报告: $REPORT_FILE"
log_info "结果目录: hadoop_results/"
echo
log_info "查看完整结果:"
log_info "  cat hadoop_results/job1_movie_stats.txt"
log_info "  cat hadoop_results/job2_user_activity.txt"
log_info "  cat hadoop_results/job3_genre_preference.txt"
echo
log_success "电影分析MapReduce项目部署成功！"
