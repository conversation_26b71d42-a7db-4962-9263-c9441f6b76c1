package com.hadoop.movieanalysis.job1;

import com.hadoop.movieanalysis.writable.RatingStatistics;
import org.apache.hadoop.io.IntWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Reducer;

import java.io.IOException;

/**
 * 电影评分统计Reducer
 * 输入: Key=MovieID, Value=RatingStatistics
 * 输出: Key=MovieID, Value=统计结果文本
 */
public class MovieRatingStatsReducer extends Reducer<IntWritable, RatingStatistics, IntWritable, Text> {

    private Text result = new Text();

    @Override
    protected void reduce(IntWritable key, Iterable<RatingStatistics> values, Context context)
            throws IOException, InterruptedException {

        // 合并所有统计信息
        RatingStatistics finalStats = new RatingStatistics();
        for (RatingStatistics stats : values) {
            finalStats.merge(stats);
        }

        // 格式化输出结果
        String output = String.format(
            "评分总数: %d, 平均评分: %.2f, 最高评分: %.1f, 最低评分: %.1f, 评分总和: %.2f",
            finalStats.getCount(),
            finalStats.getAverageRating(),
            finalStats.getMaxRating(),
            finalStats.getMinRating(),
            finalStats.getSum()
        );

        result.set(output);
        context.write(key, result);

        // 更新计数器
        context.getCounter("MovieRatingStatsReducer", "MOVIES_PROCESSED").increment(1);
        context.getCounter("MovieRatingStatsReducer", "TOTAL_RATINGS").increment(finalStats.getCount());
    }
}
