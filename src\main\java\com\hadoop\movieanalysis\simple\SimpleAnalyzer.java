package com.hadoop.movieanalysis.simple;

import java.io.*;
import java.util.*;

/**
 * 简化的电影分析器（不依赖Hadoop，用于验证算法逻辑）
 */
public class SimpleAnalyzer {
    
    public static void main(String[] args) {
        if (args.length < 2) {
            System.out.println("用法: SimpleAnalyzer <input_dir> <output_dir> [task_number]");
            System.out.println("task_number: 1=电影评分统计, 2=用户活跃度分析, 3=电影类型偏好分析, all=运行所有任务");
            return;
        }
        
        String inputDir = args[0];
        String outputDir = args[1];
        String taskType = args.length > 2 ? args[2] : "all";
        
        SimpleAnalyzer analyzer = new SimpleAnalyzer();
        
        try {
            // 创建输出目录
            new File(outputDir).mkdirs();
            
            switch (taskType.toLowerCase()) {
                case "1":
                    analyzer.analyzeMovieRatings(inputDir, outputDir);
                    break;
                case "2":
                    analyzer.analyzeUserActivity(inputDir, outputDir);
                    break;
                case "3":
                    analyzer.analyzeGenrePreference(inputDir, outputDir);
                    break;
                case "all":
                default:
                    analyzer.analyzeMovieRatings(inputDir, outputDir);
                    analyzer.analyzeUserActivity(inputDir, outputDir);
                    analyzer.analyzeGenrePreference(inputDir, outputDir);
                    break;
            }
            
            System.out.println("分析完成！结果保存在: " + outputDir);
            
        } catch (Exception e) {
            System.err.println("分析过程中出现错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 任务1：电影评分统计分析
     */
    public void analyzeMovieRatings(String inputDir, String outputDir) throws IOException {
        System.out.println("开始任务1：电影评分统计分析...");
        
        Map<Integer, RatingStats> movieStats = new HashMap<>();
        
        // 读取评分数据
        try (BufferedReader reader = new BufferedReader(new FileReader(inputDir + "/ratings.dat"))) {
            String line;
            while ((line = reader.readLine()) != null) {
                SimpleRating rating = SimpleRating.parseFromString(line);
                if (rating != null) {
                    int movieId = rating.getMovieId();
                    RatingStats stats = movieStats.getOrDefault(movieId, new RatingStats());
                    stats.addRating(rating.getRating());
                    movieStats.put(movieId, stats);
                }
            }
        }
        
        // 写入结果
        try (PrintWriter writer = new PrintWriter(new FileWriter(outputDir + "/job1_movie_stats.txt"))) {
            writer.println("电影评分统计分析结果");
            writer.println("格式: 电影ID\t评分总数\t平均评分\t最高评分\t最低评分\t评分总和");
            writer.println("========================================");
            
            for (Map.Entry<Integer, RatingStats> entry : movieStats.entrySet()) {
                int movieId = entry.getKey();
                RatingStats stats = entry.getValue();
                writer.printf("%d\t%d\t%.2f\t%.1f\t%.1f\t%.2f%n",
                    movieId,
                    stats.getCount(),
                    stats.getAverageRating(),
                    stats.getMaxRating(),
                    stats.getMinRating(),
                    stats.getSum()
                );
            }
        }
        
        System.out.println("任务1完成！处理了 " + movieStats.size() + " 部电影的评分数据");
    }
    
    /**
     * 任务2：用户活跃度分析
     */
    public void analyzeUserActivity(String inputDir, String outputDir) throws IOException {
        System.out.println("开始任务2：用户活跃度分析...");
        
        Map<Integer, Integer> userActivityCount = new HashMap<>();
        
        // 读取评分数据
        try (BufferedReader reader = new BufferedReader(new FileReader(inputDir + "/ratings.dat"))) {
            String line;
            while ((line = reader.readLine()) != null) {
                SimpleRating rating = SimpleRating.parseFromString(line);
                if (rating != null) {
                    int userId = rating.getUserId();
                    userActivityCount.put(userId, userActivityCount.getOrDefault(userId, 0) + 1);
                }
            }
        }
        
        // 写入结果
        try (PrintWriter writer = new PrintWriter(new FileWriter(outputDir + "/job2_user_activity.txt"))) {
            writer.println("用户活跃度分析结果");
            writer.println("格式: 用户ID\t评分次数\t活跃度等级");
            writer.println("========================================");
            
            for (Map.Entry<Integer, Integer> entry : userActivityCount.entrySet()) {
                int userId = entry.getKey();
                int count = entry.getValue();
                String activityLevel = getActivityLevel(count);
                writer.printf("%d\t%d\t%s%n", userId, count, activityLevel);
            }
        }
        
        System.out.println("任务2完成！分析了 " + userActivityCount.size() + " 个用户的活跃度");
    }
    
    /**
     * 任务3：电影类型偏好分析
     */
    public void analyzeGenrePreference(String inputDir, String outputDir) throws IOException {
        System.out.println("开始任务3：电影类型偏好分析...");
        
        // 读取电影数据，建立电影ID到类型的映射
        Map<Integer, String> movieGenres = new HashMap<>();
        try (BufferedReader reader = new BufferedReader(new FileReader(inputDir + "/movies.dat"))) {
            String line;
            while ((line = reader.readLine()) != null) {
                SimpleMovie movie = SimpleMovie.parseFromString(line);
                if (movie != null) {
                    movieGenres.put(movie.getMovieId(), movie.getGenres());
                }
            }
        }
        
        // 统计各类型的评分
        Map<String, RatingStats> genreStats = new HashMap<>();
        try (BufferedReader reader = new BufferedReader(new FileReader(inputDir + "/ratings.dat"))) {
            String line;
            while ((line = reader.readLine()) != null) {
                SimpleRating rating = SimpleRating.parseFromString(line);
                if (rating != null) {
                    String genres = movieGenres.get(rating.getMovieId());
                    if (genres != null) {
                        // 处理多个类型（用|分隔）
                        String[] genreArray = genres.split("\\|");
                        for (String genre : genreArray) {
                            genre = genre.trim();
                            if (!genre.isEmpty()) {
                                RatingStats stats = genreStats.getOrDefault(genre, new RatingStats());
                                stats.addRating(rating.getRating());
                                genreStats.put(genre, stats);
                            }
                        }
                    }
                }
            }
        }
        
        // 写入结果
        try (PrintWriter writer = new PrintWriter(new FileWriter(outputDir + "/job3_genre_preference.txt"))) {
            writer.println("电影类型偏好分析结果");
            writer.println("格式: 电影类型\t评分总数\t平均评分\t最高评分\t最低评分");
            writer.println("========================================");
            
            // 按平均评分降序排序
            List<Map.Entry<String, RatingStats>> sortedGenres = new ArrayList<>(genreStats.entrySet());
            sortedGenres.sort((a, b) -> Double.compare(b.getValue().getAverageRating(), a.getValue().getAverageRating()));
            
            for (Map.Entry<String, RatingStats> entry : sortedGenres) {
                String genre = entry.getKey();
                RatingStats stats = entry.getValue();
                writer.printf("%s\t%d\t%.2f\t%.1f\t%.1f%n",
                    genre,
                    stats.getCount(),
                    stats.getAverageRating(),
                    stats.getMaxRating(),
                    stats.getMinRating()
                );
            }
        }
        
        System.out.println("任务3完成！分析了 " + genreStats.size() + " 种电影类型的偏好");
    }
    
    /**
     * 根据评分次数判断活跃度等级
     */
    private String getActivityLevel(int count) {
        if (count >= 100) {
            return "非常活跃";
        } else if (count >= 50) {
            return "活跃";
        } else if (count >= 20) {
            return "一般活跃";
        } else {
            return "不活跃";
        }
    }
    
    /**
     * 评分统计信息内部类
     */
    private static class RatingStats {
        private long count = 0;
        private double sum = 0.0;
        private double maxRating = Double.MIN_VALUE;
        private double minRating = Double.MAX_VALUE;

        public void addRating(double rating) {
            count++;
            sum += rating;
            maxRating = Math.max(maxRating, rating);
            minRating = Math.min(minRating, rating);
        }

        public long getCount() { return count; }
        public double getSum() { return sum; }
        public double getMaxRating() { return maxRating; }
        public double getMinRating() { return minRating; }
        public double getAverageRating() { return count > 0 ? sum / count : 0.0; }
    }
}
