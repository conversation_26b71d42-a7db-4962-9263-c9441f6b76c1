package com.hadoop.movieanalysis.job1;

import com.hadoop.movieanalysis.model.Rating;
import com.hadoop.movieanalysis.writable.RatingStatistics;
import org.apache.hadoop.io.IntWritable;
import org.apache.hadoop.io.LongWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Mapper;

import java.io.IOException;

/**
 * 电影评分统计Mapper
 * 输入: 评分数据 (UserID::MovieID::Rating::Timestamp)
 * 输出: Key=MovieID, Value=RatingStatistics
 */
public class MovieRatingStatsMapper extends Mapper<LongWritable, Text, IntWritable, RatingStatistics> {

    private IntWritable movieId = new IntWritable();
    private RatingStatistics stats = new RatingStatistics();

    @Override
    protected void map(LongWritable key, Text value, Context context) 
            throws IOException, InterruptedException {
        
        String line = value.toString().trim();
        
        // 跳过空行
        if (line.isEmpty()) {
            return;
        }

        try {
            // 解析评分数据
            Rating rating = Rating.parseFromString(line);
            if (rating != null) {
                // 设置电影ID作为key
                movieId.set(rating.getMovieId());
                
                // 创建包含单个评分的统计对象
                stats = new RatingStatistics();
                stats.addRating(rating.getRating());
                
                // 输出到context
                context.write(movieId, stats);
            }
        } catch (Exception e) {
            // 记录错误但继续处理其他记录
            context.getCounter("MovieRatingStatsMapper", "PARSE_ERRORS").increment(1);
        }
    }
}
