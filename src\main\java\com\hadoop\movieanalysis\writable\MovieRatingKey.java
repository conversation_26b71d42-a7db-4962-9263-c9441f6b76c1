package com.hadoop.movieanalysis.writable;

import org.apache.hadoop.io.WritableComparable;
import java.io.DataInput;
import java.io.DataOutput;
import java.io.IOException;

/**
 * 电影评分复合键
 * 用于按电影ID和评分进行排序
 */
public class MovieRatingKey implements WritableComparable<MovieRatingKey> {
    private int movieId;
    private double rating;

    public MovieRatingKey() {
        // 默认构造函数
    }

    public MovieRatingKey(int movieId, double rating) {
        this.movieId = movieId;
        this.rating = rating;
    }

    @Override
    public void write(DataOutput out) throws IOException {
        out.writeInt(movieId);
        out.writeDouble(rating);
    }

    @Override
    public void readFields(DataInput in) throws IOException {
        movieId = in.readInt();
        rating = in.readDouble();
    }

    @Override
    public int compareTo(MovieRatingKey other) {
        // 首先按电影ID排序
        int movieIdComparison = Integer.compare(this.movieId, other.movieId);
        if (movieIdComparison != 0) {
            return movieIdComparison;
        }
        // 如果电影ID相同，按评分降序排序（高评分在前）
        return Double.compare(other.rating, this.rating);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        MovieRatingKey that = (MovieRatingKey) obj;
        return movieId == that.movieId && Double.compare(that.rating, rating) == 0;
    }

    @Override
    public int hashCode() {
        int result = Integer.hashCode(movieId);
        result = 31 * result + Double.hashCode(rating);
        return result;
    }

    @Override
    public String toString() {
        return movieId + "," + rating;
    }

    // Getters and Setters
    public int getMovieId() {
        return movieId;
    }

    public void setMovieId(int movieId) {
        this.movieId = movieId;
    }

    public double getRating() {
        return rating;
    }

    public void setRating(double rating) {
        this.rating = rating;
    }
}
