package com.hadoop.movieanalysis.test;

import com.hadoop.movieanalysis.model.Movie;
import com.hadoop.movieanalysis.model.Rating;
import com.hadoop.movieanalysis.model.User;

/**
 * 数据模型测试类
 */
public class DataModelTest {
    
    public static void main(String[] args) {
        System.out.println("========================================");
        System.out.println("数据模型测试");
        System.out.println("========================================");
        
        // 测试Movie类
        testMovie();
        
        // 测试Rating类
        testRating();
        
        // 测试User类
        testUser();
        
        System.out.println("========================================");
        System.out.println("所有测试完成！");
        System.out.println("========================================");
    }
    
    private static void testMovie() {
        System.out.println("测试Movie类...");
        
        String movieLine = "1::Toy Story (1995)::Animation|Children's|Comedy";
        Movie movie = Movie.parseFromString(movieLine);
        
        if (movie != null) {
            System.out.println("解析成功: " + movie.toString());
            System.out.println("电影ID: " + movie.getMovieId());
            System.out.println("标题: " + movie.getTitle());
            System.out.println("类型: " + movie.getGenres());
        } else {
            System.out.println("Movie解析失败！");
        }
        System.out.println();
    }
    
    private static void testRating() {
        System.out.println("测试Rating类...");
        
        String ratingLine = "1::1::5::978300760";
        Rating rating = Rating.parseFromString(ratingLine);
        
        if (rating != null) {
            System.out.println("解析成功: " + rating.toString());
            System.out.println("用户ID: " + rating.getUserId());
            System.out.println("电影ID: " + rating.getMovieId());
            System.out.println("评分: " + rating.getRating());
            System.out.println("时间戳: " + rating.getTimestamp());
        } else {
            System.out.println("Rating解析失败！");
        }
        System.out.println();
    }
    
    private static void testUser() {
        System.out.println("测试User类...");
        
        String userLine = "1::F::1::10::48067";
        User user = User.parseFromString(userLine);
        
        if (user != null) {
            System.out.println("解析成功: " + user.toString());
            System.out.println("用户ID: " + user.getUserId());
            System.out.println("性别: " + user.getGender());
            System.out.println("年龄: " + user.getAge());
            System.out.println("职业: " + user.getOccupation());
            System.out.println("邮编: " + user.getZipCode());
        } else {
            System.out.println("User解析失败！");
        }
        System.out.println();
    }
}
