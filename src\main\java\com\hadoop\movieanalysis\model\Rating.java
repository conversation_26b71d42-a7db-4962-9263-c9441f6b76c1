package com.hadoop.movieanalysis.model;

import org.apache.hadoop.io.Writable;
import java.io.DataInput;
import java.io.DataOutput;
import java.io.IOException;

/**
 * 评分数据模型
 * 格式: UserID::MovieID::Rating::Timestamp
 */
public class Rating implements Writable {
    private int userId;
    private int movieId;
    private double rating;
    private long timestamp;

    public Rating() {
        // 默认构造函数
    }

    public Rating(int userId, int movieId, double rating, long timestamp) {
        this.userId = userId;
        this.movieId = movieId;
        this.rating = rating;
        this.timestamp = timestamp;
    }

    /**
     * 从字符串解析评分信息
     * @param line 输入行，格式: UserID::MovieID::Rating::Timestamp
     * @return Rating对象
     */
    public static Rating parseFromString(String line) {
        String[] parts = line.split("::");
        if (parts.length >= 4) {
            int userId = Integer.parseInt(parts[0]);
            int movieId = Integer.parseInt(parts[1]);
            double rating = Double.parseDouble(parts[2]);
            long timestamp = Long.parseLong(parts[3]);
            return new Rating(userId, movieId, rating, timestamp);
        }
        return null;
    }

    @Override
    public void write(DataOutput out) throws IOException {
        out.writeInt(userId);
        out.writeInt(movieId);
        out.writeDouble(rating);
        out.writeLong(timestamp);
    }

    @Override
    public void readFields(DataInput in) throws IOException {
        userId = in.readInt();
        movieId = in.readInt();
        rating = in.readDouble();
        timestamp = in.readLong();
    }

    // Getters and Setters
    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public int getMovieId() {
        return movieId;
    }

    public void setMovieId(int movieId) {
        this.movieId = movieId;
    }

    public double getRating() {
        return rating;
    }

    public void setRating(double rating) {
        this.rating = rating;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    @Override
    public String toString() {
        return userId + "::" + movieId + "::" + rating + "::" + timestamp;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Rating rating1 = (Rating) obj;
        return userId == rating1.userId && movieId == rating1.movieId;
    }

    @Override
    public int hashCode() {
        return Integer.hashCode(userId) * 31 + Integer.hashCode(movieId);
    }
}
