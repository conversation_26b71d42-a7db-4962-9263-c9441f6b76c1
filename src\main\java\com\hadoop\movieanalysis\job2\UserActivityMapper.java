package com.hadoop.movieanalysis.job2;

import com.hadoop.movieanalysis.model.Rating;
import org.apache.hadoop.io.IntWritable;
import org.apache.hadoop.io.LongWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Mapper;

import java.io.IOException;

/**
 * 用户活跃度分析Mapper
 * 输入: 评分数据 (UserID::MovieID::Rating::Timestamp)
 * 输出: Key=UserID, Value=1 (用于计数)
 */
public class UserActivityMapper extends Mapper<LongWritable, Text, IntWritable, IntWritable> {

    private IntWritable userId = new IntWritable();
    private IntWritable one = new IntWritable(1);

    @Override
    protected void map(LongWritable key, Text value, Context context) 
            throws IOException, InterruptedException {
        
        String line = value.toString().trim();
        
        // 跳过空行
        if (line.isEmpty()) {
            return;
        }

        try {
            // 解析评分数据
            Rating rating = Rating.parseFromString(line);
            if (rating != null) {
                // 设置用户ID作为key，1作为value进行计数
                userId.set(rating.getUserId());
                context.write(userId, one);
            }
        } catch (Exception e) {
            // 记录错误但继续处理其他记录
            context.getCounter("UserActivityMapper", "PARSE_ERRORS").increment(1);
        }
    }
}
