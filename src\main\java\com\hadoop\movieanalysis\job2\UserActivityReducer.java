package com.hadoop.movieanalysis.job2;

import org.apache.hadoop.io.IntWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Reducer;

import java.io.IOException;

/**
 * 用户活跃度分析Reducer
 * 输入: Key=UserID, Value=评分次数
 * 输出: Key=UserID, Value=活跃度描述
 */
public class UserActivityReducer extends Reducer<IntWritable, IntWritable, IntWritable, Text> {

    private Text result = new Text();

    @Override
    protected void reduce(IntWritable key, Iterable<IntWritable> values, Context context)
            throws IOException, InterruptedException {

        int totalRatings = 0;
        
        // 计算用户总评分次数
        for (IntWritable value : values) {
            totalRatings += value.get();
        }

        // 根据评分次数判断用户活跃度
        String activityLevel;
        if (totalRatings >= 100) {
            activityLevel = "非常活跃";
        } else if (totalRatings >= 50) {
            activityLevel = "活跃";
        } else if (totalRatings >= 20) {
            activityLevel = "一般活跃";
        } else {
            activityLevel = "不活跃";
        }

        // 格式化输出结果
        String output = String.format("评分次数: %d, 活跃度: %s", totalRatings, activityLevel);
        result.set(output);
        context.write(key, result);

        // 更新计数器
        context.getCounter("UserActivityReducer", "USERS_PROCESSED").increment(1);
        context.getCounter("UserActivityReducer", "TOTAL_RATINGS").increment(totalRatings);
        
        // 按活跃度分类计数
        if (totalRatings >= 100) {
            context.getCounter("UserActivityReducer", "VERY_ACTIVE_USERS").increment(1);
        } else if (totalRatings >= 50) {
            context.getCounter("UserActivityReducer", "ACTIVE_USERS").increment(1);
        } else if (totalRatings >= 20) {
            context.getCounter("UserActivityReducer", "MODERATE_USERS").increment(1);
        } else {
            context.getCounter("UserActivityReducer", "INACTIVE_USERS").increment(1);
        }
    }
}
