package com.hadoop.movieanalysis.job3;

import com.hadoop.movieanalysis.model.Movie;
import com.hadoop.movieanalysis.model.Rating;
import org.apache.hadoop.io.LongWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Mapper;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 电影类型偏好分析Mapper
 * 需要处理两种输入文件：movies.dat 和 ratings.dat
 * 输出: Key=Genre, Value=Rating
 */
public class GenrePreferenceMapper extends Mapper<LongWritable, Text, Text, Text> {

    private Text genreKey = new Text();
    private Text ratingValue = new Text();
    
    // 存储电影ID到类型的映射
    private Map<Integer, String> movieGenres = new HashMap<>();

    @Override
    protected void setup(Context context) throws IOException, InterruptedException {
        super.setup(context);
        // 在setup阶段可以加载电影数据，但这里我们使用不同的方法
        // 实际项目中可以使用DistributedCache来共享电影数据
    }

    @Override
    protected void map(LongWritable key, Text value, Context context) 
            throws IOException, InterruptedException {
        
        String line = value.toString().trim();
        
        // 跳过空行
        if (line.isEmpty()) {
            return;
        }

        try {
            // 判断是电影数据还是评分数据
            String[] parts = line.split("::");
            
            if (parts.length >= 3 && isMovieData(parts)) {
                // 处理电影数据：MovieID::Title::Genres
                Movie movie = Movie.parseFromString(line);
                if (movie != null) {
                    // 存储电影类型信息，标记为电影数据
                    String output = "MOVIE:" + movie.getMovieId() + ":" + movie.getGenres();
                    genreKey.set("MOVIE_DATA");
                    ratingValue.set(output);
                    context.write(genreKey, ratingValue);
                }
            } else if (parts.length >= 4) {
                // 处理评分数据：UserID::MovieID::Rating::Timestamp
                Rating rating = Rating.parseFromString(line);
                if (rating != null) {
                    // 输出评分数据，标记为评分数据
                    String output = "RATING:" + rating.getMovieId() + ":" + rating.getRating();
                    genreKey.set("RATING_DATA");
                    ratingValue.set(output);
                    context.write(genreKey, ratingValue);
                }
            }
        } catch (Exception e) {
            // 记录错误但继续处理其他记录
            context.getCounter("GenrePreferenceMapper", "PARSE_ERRORS").increment(1);
        }
    }

    /**
     * 判断是否为电影数据
     * 电影数据的第三个字段包含类型信息（包含|分隔符）
     */
    private boolean isMovieData(String[] parts) {
        if (parts.length >= 3) {
            // 电影数据的类型字段通常包含|分隔符
            return parts[2].contains("|") || parts[2].matches("[A-Za-z]+");
        }
        return false;
    }
}
