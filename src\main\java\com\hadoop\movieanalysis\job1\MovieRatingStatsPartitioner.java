package com.hadoop.movieanalysis.job1;

import com.hadoop.movieanalysis.writable.RatingStatistics;
import org.apache.hadoop.io.IntWritable;
import org.apache.hadoop.mapreduce.Partitioner;

/**
 * 电影评分统计自定义分区器
 * 根据电影ID进行分区，确保相同电影的数据发送到同一个Reducer
 */
public class MovieRatingStatsPartitioner extends Partitioner<IntWritable, RatingStatistics> {

    @Override
    public int getPartition(IntWritable key, RatingStatistics value, int numPartitions) {
        // 使用电影ID的哈希值进行分区
        // 确保相同电影ID的数据发送到同一个Reducer
        return (key.get() & Integer.MAX_VALUE) % numPartitions;
    }
}
