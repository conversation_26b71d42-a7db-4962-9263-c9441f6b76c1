package com.hadoop.movieanalysis.simple;

/**
 * 简化的评分数据模型（不依赖Hadoop）
 */
public class SimpleRating {
    private int userId;
    private int movieId;
    private double rating;
    private long timestamp;

    public SimpleRating() {
        // 默认构造函数
    }

    public SimpleRating(int userId, int movieId, double rating, long timestamp) {
        this.userId = userId;
        this.movieId = movieId;
        this.rating = rating;
        this.timestamp = timestamp;
    }

    /**
     * 从字符串解析评分信息
     * @param line 输入行，格式: UserID::MovieID::Rating::Timestamp
     * @return SimpleRating对象
     */
    public static SimpleRating parseFromString(String line) {
        String[] parts = line.split("::");
        if (parts.length >= 4) {
            int userId = Integer.parseInt(parts[0]);
            int movieId = Integer.parseInt(parts[1]);
            double rating = Double.parseDouble(parts[2]);
            long timestamp = Long.parseLong(parts[3]);
            return new SimpleRating(userId, movieId, rating, timestamp);
        }
        return null;
    }

    // Getters and Setters
    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public int getMovieId() {
        return movieId;
    }

    public void setMovieId(int movieId) {
        this.movieId = movieId;
    }

    public double getRating() {
        return rating;
    }

    public void setRating(double rating) {
        this.rating = rating;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    @Override
    public String toString() {
        return userId + "::" + movieId + "::" + rating + "::" + timestamp;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        SimpleRating rating1 = (SimpleRating) obj;
        return userId == rating1.userId && movieId == rating1.movieId;
    }

    @Override
    public int hashCode() {
        return Integer.hashCode(userId) * 31 + Integer.hashCode(movieId);
    }
}
