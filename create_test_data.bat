@echo off
REM 创建测试数据集
echo 创建测试数据集...

set TEST_DIR=%~dp0test_data
if not exist "%TEST_DIR%" mkdir "%TEST_DIR%"

REM 创建测试电影数据
echo 1::Toy Story (1995)::Animation^|Children's^|Comedy > "%TEST_DIR%\movies.dat"
echo 2::<PERSON><PERSON><PERSON> (1995)::Adventure^|Children's^|Fantasy >> "%TEST_DIR%\movies.dat"
echo 3::Grumpier Old Men (1995)::Comedy^|Romance >> "%TEST_DIR%\movies.dat"
echo 4::Waiting to Exhale (1995)::Comedy^|Drama >> "%TEST_DIR%\movies.dat"
echo 5::Father of the Bride Part II (1995)::Comedy >> "%TEST_DIR%\movies.dat"

REM 创建测试评分数据
echo 1::1::5::978300760 > "%TEST_DIR%\ratings.dat"
echo 1::2::3::978302109 >> "%TEST_DIR%\ratings.dat"
echo 1::3::4::978301968 >> "%TEST_DIR%\ratings.dat"
echo 2::1::4::978298709 >> "%TEST_DIR%\ratings.dat"
echo 2::2::5::978299000 >> "%TEST_DIR%\ratings.dat"
echo 2::4::3::978299620 >> "%TEST_DIR%\ratings.dat"
echo 3::1::3::978298147 >> "%TEST_DIR%\ratings.dat"
echo 3::3::5::978298430 >> "%TEST_DIR%\ratings.dat"
echo 3::5::4::978297867 >> "%TEST_DIR%\ratings.dat"
echo 4::2::2::978294008 >> "%TEST_DIR%\ratings.dat"
echo 4::4::4::978293924 >> "%TEST_DIR%\ratings.dat"
echo 4::5::5::978294282 >> "%TEST_DIR%\ratings.dat"

REM 创建测试用户数据
echo 1::F::1::10::48067 > "%TEST_DIR%\users.dat"
echo 2::M::56::16::70072 >> "%TEST_DIR%\users.dat"
echo 3::M::25::15::55117 >> "%TEST_DIR%\users.dat"
echo 4::M::45::7::02460 >> "%TEST_DIR%\users.dat"

echo 测试数据集创建完成！
echo 位置: %TEST_DIR%
echo 文件:
echo - movies.dat (5部电影)
echo - ratings.dat (12个评分)
echo - users.dat (4个用户)

pause
