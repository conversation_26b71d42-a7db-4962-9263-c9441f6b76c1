# 电影网站用户影评分析 - MapReduce高阶编程

## 项目概述

本项目使用MapReduce框架对电影网站的用户影评数据进行大数据分析，实现了三个主要的分析任务：

1. **电影评分统计分析** - 计算每部电影的评分统计信息（最值、平均分、总数等）
2. **用户活跃度分析** - 分析用户的评分行为，判断用户活跃程度
3. **电影类型偏好分析** - 统计不同电影类型的评分情况

## 技术特点

### MapReduce高阶优化策略
- **Combiner优化**: 在Map端进行本地聚合，减少网络传输数据量
- **Partitioner优化**: 自定义分区策略，确保数据均匀分布到各个Reducer
- **自定义WritableComparable**: 实现复合键排序，支持复杂的数据处理需求

### 项目架构
```
src/main/java/com/hadoop/movieanalysis/
├── model/                          # 数据模型
│   ├── Movie.java                  # 电影数据模型
│   ├── Rating.java                 # 评分数据模型
│   └── User.java                   # 用户数据模型
├── writable/                       # 自定义Writable类
│   ├── MovieRatingKey.java         # 电影评分复合键
│   └── RatingStatistics.java       # 评分统计信息
├── job1/                           # 任务1：电影评分统计
│   ├── MovieRatingStatsMapper.java
│   ├── MovieRatingStatsCombiner.java
│   ├── MovieRatingStatsReducer.java
│   └── MovieRatingStatsPartitioner.java
├── job2/                           # 任务2：用户活跃度分析
│   ├── UserActivityMapper.java
│   ├── UserActivityCombiner.java
│   └── UserActivityReducer.java
├── job3/                           # 任务3：电影类型偏好分析
│   ├── GenrePreferenceMapper.java
│   └── GenrePreferenceReducer.java
└── MovieAnalysisDriver.java        # 主驱动程序
```

## 数据集说明

项目使用MovieLens数据集，包含三个文件：

1. **movies.dat**: 电影信息
   - 格式: `MovieID::Title::Genres`
   - 示例: `1::Toy Story (1995)::Animation|Children's|Comedy`

2. **ratings.dat**: 用户评分
   - 格式: `UserID::MovieID::Rating::Timestamp`
   - 示例: `1::1193::5::978300760`

3. **users.dat**: 用户信息
   - 格式: `UserID::Gender::Age::Occupation::Zip-code`
   - 示例: `1::F::1::10::48067`

## 环境要求

- Java 8 或更高版本
- Apache Hadoop 3.1.3
- Apache Maven 3.6+
- Windows 10/11 (脚本为Windows环境设计)

## 安装和运行

### 1. 环境准备

确保已安装并配置好以下环境：
- Java JDK 8+
- Hadoop 3.1.3
- Maven

### 2. 项目编译

```bash
mvn clean package
```

### 3. 数据准备

将数据文件放置到 `input/` 目录：
- `input/movies.dat`
- `input/ratings.dat`
- `input/users.dat`

### 4. 运行分析

#### 方式1：使用批处理脚本（推荐）

```bash
# 运行所有任务
run_analysis.bat

# 运行特定任务
run_analysis.bat 1    # 电影评分统计
run_analysis.bat 2    # 用户活跃度分析
run_analysis.bat 3    # 电影类型偏好分析
```

#### 方式2：本地模式运行

```bash
run_local.bat
```

#### 方式3：手动运行

```bash
java -cp target/movie-analysis-mapreduce-1.0-SNAPSHOT.jar com.hadoop.movieanalysis.MovieAnalysisDriver input output all
```

### 5. 提交到Hadoop集群

```bash
# 上传数据到HDFS
hdfs dfs -mkdir /input
hdfs dfs -put input/* /input/

# 提交任务到集群
hadoop jar target/movie-analysis-mapreduce-1.0-SNAPSHOT.jar com.hadoop.movieanalysis.MovieAnalysisDriver /input /output all

# 查看结果
hdfs dfs -cat /output/job1_movie_stats/part-r-00000
```

## 输出结果

### 任务1：电影评分统计
```
1	评分总数: 2077, 平均评分: 4.15, 最高评分: 5.0, 最低评分: 1.0, 评分总和: 8619.00
2	评分总数: 701, 平均评分: 3.20, 最高评分: 5.0, 最低评分: 1.0, 评分总和: 2244.00
```

### 任务2：用户活跃度分析
```
1	评分次数: 53, 活跃度: 一般活跃
2	评分次数: 182, 活跃度: 非常活跃
```

### 任务3：电影类型偏好分析
```
Action	评分总数: 15828, 平均评分: 3.34, 最高评分: 5.0, 最低评分: 1.0
Comedy	评分总数: 20781, 平均评分: 3.42, 最高评分: 5.0, 最低评分: 1.0
```

## 性能优化

1. **Combiner使用**: 所有任务都使用了Combiner进行本地聚合
2. **自定义Partitioner**: 确保数据均匀分布
3. **数据压缩**: 可配置中间结果压缩
4. **内存优化**: 合理设置JVM参数

## 扩展功能

- 支持多种输出格式
- 可配置的统计阈值
- 详细的性能计数器
- 错误处理和日志记录

## 作者信息

- 课程：大数据技术与应用
- 作业：MapReduce高阶编程
- 要求：使用Combiner和Partitioner优化，完成求最值、范围值、求和等分析
