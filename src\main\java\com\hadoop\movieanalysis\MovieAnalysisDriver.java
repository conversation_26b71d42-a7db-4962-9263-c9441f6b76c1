package com.hadoop.movieanalysis;

import com.hadoop.movieanalysis.job1.*;
import com.hadoop.movieanalysis.job2.*;
import com.hadoop.movieanalysis.job3.*;
import com.hadoop.movieanalysis.writable.RatingStatistics;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.io.IntWritable;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Job;
import org.apache.hadoop.mapreduce.lib.input.FileInputFormat;
import org.apache.hadoop.mapreduce.lib.input.TextInputFormat;
import org.apache.hadoop.mapreduce.lib.output.FileOutputFormat;
import org.apache.hadoop.mapreduce.lib.output.TextOutputFormat;
import org.apache.hadoop.util.GenericOptionsParser;

/**
 * 电影分析MapReduce主驱动程序
 * 包含三个分析任务：
 * 1. 电影评分统计分析
 * 2. 用户活跃度分析  
 * 3. 电影类型偏好分析
 */
public class MovieAnalysisDriver {

    public static void main(String[] args) throws Exception {
        Configuration conf = new Configuration();
        String[] otherArgs = new GenericOptionsParser(conf, args).getRemainingArgs();
        
        if (otherArgs.length < 2) {
            System.err.println("用法: MovieAnalysisDriver <input path> <output path> [job_number]");
            System.err.println("job_number: 1=电影评分统计, 2=用户活跃度分析, 3=电影类型偏好分析, all=运行所有任务");
            System.exit(2);
        }

        String inputPath = otherArgs[0];
        String outputPath = otherArgs[1];
        String jobType = otherArgs.length > 2 ? otherArgs[2] : "all";

        boolean success = true;

        switch (jobType.toLowerCase()) {
            case "1":
                success = runMovieRatingStatsJob(conf, inputPath, outputPath + "/job1_movie_stats");
                break;
            case "2":
                success = runUserActivityJob(conf, inputPath, outputPath + "/job2_user_activity");
                break;
            case "3":
                success = runGenrePreferenceJob(conf, inputPath, outputPath + "/job3_genre_preference");
                break;
            case "all":
            default:
                success = runMovieRatingStatsJob(conf, inputPath, outputPath + "/job1_movie_stats") &&
                         runUserActivityJob(conf, inputPath, outputPath + "/job2_user_activity") &&
                         runGenrePreferenceJob(conf, inputPath, outputPath + "/job3_genre_preference");
                break;
        }

        System.exit(success ? 0 : 1);
    }

    /**
     * 运行电影评分统计任务
     */
    private static boolean runMovieRatingStatsJob(Configuration conf, String inputPath, String outputPath) 
            throws Exception {
        
        System.out.println("开始运行任务1: 电影评分统计分析");
        
        Job job = Job.getInstance(conf, "movie-rating-statistics");
        job.setJarByClass(MovieAnalysisDriver.class);

        // 设置Mapper、Combiner、Reducer
        job.setMapperClass(MovieRatingStatsMapper.class);
        job.setCombinerClass(MovieRatingStatsCombiner.class);
        job.setReducerClass(MovieRatingStatsReducer.class);
        job.setPartitionerClass(MovieRatingStatsPartitioner.class);

        // 设置输出类型
        job.setMapOutputKeyClass(IntWritable.class);
        job.setMapOutputValueClass(RatingStatistics.class);
        job.setOutputKeyClass(IntWritable.class);
        job.setOutputValueClass(Text.class);

        // 设置输入输出格式
        job.setInputFormatClass(TextInputFormat.class);
        job.setOutputFormatClass(TextOutputFormat.class);

        // 设置输入输出路径
        FileInputFormat.addInputPath(job, new Path(inputPath + "/ratings.dat"));
        Path output = new Path(outputPath);
        FileOutputFormat.setOutputPath(job, output);

        // 删除输出目录（如果存在）
        FileSystem fs = FileSystem.get(conf);
        if (fs.exists(output)) {
            fs.delete(output, true);
        }

        boolean success = job.waitForCompletion(true);
        System.out.println("任务1完成，状态: " + (success ? "成功" : "失败"));
        return success;
    }

    /**
     * 运行用户活跃度分析任务
     */
    private static boolean runUserActivityJob(Configuration conf, String inputPath, String outputPath) 
            throws Exception {
        
        System.out.println("开始运行任务2: 用户活跃度分析");
        
        Job job = Job.getInstance(conf, "user-activity-analysis");
        job.setJarByClass(MovieAnalysisDriver.class);

        // 设置Mapper、Combiner、Reducer
        job.setMapperClass(UserActivityMapper.class);
        job.setCombinerClass(UserActivityCombiner.class);
        job.setReducerClass(UserActivityReducer.class);

        // 设置输出类型
        job.setMapOutputKeyClass(IntWritable.class);
        job.setMapOutputValueClass(IntWritable.class);
        job.setOutputKeyClass(IntWritable.class);
        job.setOutputValueClass(Text.class);

        // 设置输入输出格式
        job.setInputFormatClass(TextInputFormat.class);
        job.setOutputFormatClass(TextOutputFormat.class);

        // 设置输入输出路径
        FileInputFormat.addInputPath(job, new Path(inputPath + "/ratings.dat"));
        Path output = new Path(outputPath);
        FileOutputFormat.setOutputPath(job, output);

        // 删除输出目录（如果存在）
        FileSystem fs = FileSystem.get(conf);
        if (fs.exists(output)) {
            fs.delete(output, true);
        }

        boolean success = job.waitForCompletion(true);
        System.out.println("任务2完成，状态: " + (success ? "成功" : "失败"));
        return success;
    }

    /**
     * 运行电影类型偏好分析任务
     */
    private static boolean runGenrePreferenceJob(Configuration conf, String inputPath, String outputPath)
            throws Exception {

        System.out.println("开始运行任务3: 电影类型偏好分析");

        Job job = Job.getInstance(conf, "genre-preference-analysis");
        job.setJarByClass(MovieAnalysisDriver.class);

        // 设置Mapper、Reducer
        job.setMapperClass(GenrePreferenceMapper.class);
        job.setReducerClass(GenrePreferenceReducer.class);

        // 设置输出类型
        job.setMapOutputKeyClass(Text.class);
        job.setMapOutputValueClass(Text.class);
        job.setOutputKeyClass(Text.class);
        job.setOutputValueClass(Text.class);

        // 设置输入输出格式
        job.setInputFormatClass(TextInputFormat.class);
        job.setOutputFormatClass(TextOutputFormat.class);

        // 设置输入输出路径（需要同时读取电影和评分数据）
        FileInputFormat.addInputPath(job, new Path(inputPath + "/movies.dat"));
        FileInputFormat.addInputPath(job, new Path(inputPath + "/ratings.dat"));
        Path output = new Path(outputPath);
        FileOutputFormat.setOutputPath(job, output);

        // 删除输出目录（如果存在）
        FileSystem fs = FileSystem.get(conf);
        if (fs.exists(output)) {
            fs.delete(output, true);
        }

        boolean success = job.waitForCompletion(true);
        System.out.println("任务3完成，状态: " + (success ? "成功" : "失败"));
        return success;
    }
}
