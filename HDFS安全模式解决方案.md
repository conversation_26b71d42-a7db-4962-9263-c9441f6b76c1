# HDFS安全模式问题解决方案

## 🚨 问题描述

```bash
hdfs dfs -mkdir /input
mkdir: Cannot create directory /input. Name node is in safe mode.
```

这个错误表示HDFS的NameNode处于安全模式（Safe Mode），在此模式下HDFS是只读的，无法进行写操作。

## 🔍 问题原因

HDFS进入安全模式的常见原因：
1. **集群刚启动** - NameNode正在等待DataNode汇报块信息
2. **数据块副本不足** - 某些数据块的副本数量低于最小要求
3. **磁盘空间不足** - DataNode磁盘使用率过高
4. **网络问题** - DataNode与NameNode通信异常
5. **手动开启** - 管理员手动设置了安全模式

## 🛠️ 解决方案

### 方案1：检查安全模式状态（必须先执行）

```bash
# 检查安全模式状态
hdfs dfsadmin -safemode get

# 查看详细的安全模式信息
hdfs dfsadmin -report
```

**预期输出**：
```
Safe mode is ON
# 或
Safe mode is OFF
```

### 方案2：等待自动退出（推荐首选）

```bash
# 查看安全模式详细状态
hdfs dfsadmin -safemode get

# 查看集群状态，了解为什么还在安全模式
hdfs dfsadmin -report

# 等待一段时间（通常1-5分钟）后再次检查
sleep 60
hdfs dfsadmin -safemode get
```

**如果显示类似信息**：
```
Safe mode is ON. The reported blocks 1000 needs additional 100 blocks to reach the threshold 0.999f of total blocks 1100.
```

这表示需要等待更多DataNode汇报块信息。

### 方案3：强制退出安全模式（谨慎使用）

⚠️ **警告**：只有在确认集群状态正常的情况下才使用此方法！

```bash
# 强制退出安全模式
hdfs dfsadmin -safemode leave

# 验证是否成功退出
hdfs dfsadmin -safemode get
```

**预期输出**：
```
Safe mode is OFF
```

### 方案4：检查和修复集群问题

#### 4.1 检查DataNode状态

```bash
# 查看所有DataNode状态
hdfs dfsadmin -report

# 查看活跃的DataNode
yarn node -list

# 检查Java进程
jps
```

**预期输出应包含**：
```
Live datanodes (3):
Name: *************:9866 (worker1)
Name: *************:9866 (worker2)
Name: *************:9866 (worker3)
```

#### 4.2 检查磁盘空间

```bash
# 检查HDFS磁盘使用情况
hdfs dfs -df -h

# 检查本地磁盘空间
df -h

# 检查HDFS目录使用情况
hdfs dfs -du -h /
```

#### 4.3 检查网络连接

```bash
# 检查NameNode和DataNode之间的连接
# 在NameNode上执行
netstat -tlnp | grep 9000

# 在DataNode上执行
telnet namenode-ip 9000
```

### 方案5：重启服务（最后手段）

如果以上方法都无效，可以考虑重启HDFS服务：

```bash
# 停止HDFS服务
$HADOOP_HOME/sbin/stop-dfs.sh

# 等待几秒
sleep 10

# 启动HDFS服务
$HADOOP_HOME/sbin/start-dfs.sh

# 等待服务完全启动（约1-2分钟）
sleep 120

# 检查服务状态
jps
hdfs dfsadmin -safemode get
```

## 🔧 完整的故障排除流程

### 步骤1：诊断问题

```bash
echo "========================================="
echo "HDFS安全模式诊断"
echo "========================================="

# 检查安全模式状态
echo "1. 安全模式状态:"
hdfs dfsadmin -safemode get

# 检查集群报告
echo "2. 集群状态:"
hdfs dfsadmin -report | head -20

# 检查Java进程
echo "3. Hadoop进程状态:"
jps | grep -E "(NameNode|DataNode|SecondaryNameNode)"

# 检查磁盘空间
echo "4. 磁盘空间:"
df -h | grep -E "(/$|/home|/opt)"
```

### 步骤2：尝试解决

```bash
echo "========================================="
echo "尝试解决安全模式问题"
echo "========================================="

# 方法1：等待自动退出（等待2分钟）
echo "方法1: 等待自动退出安全模式..."
for i in {1..12}; do
    status=$(hdfs dfsadmin -safemode get)
    echo "第${i}次检查: $status"
    if [[ $status == *"OFF"* ]]; then
        echo "✓ 安全模式已自动退出"
        break
    fi
    sleep 10
done

# 检查当前状态
current_status=$(hdfs dfsadmin -safemode get)
if [[ $current_status == *"OFF"* ]]; then
    echo "✓ 安全模式已关闭，可以继续操作"
else
    echo "⚠ 安全模式仍然开启，尝试强制退出..."
    
    # 方法2：强制退出
    hdfs dfsadmin -safemode leave
    sleep 5
    
    # 再次检查
    final_status=$(hdfs dfsadmin -safemode get)
    if [[ $final_status == *"OFF"* ]]; then
        echo "✓ 强制退出安全模式成功"
    else
        echo "✗ 无法退出安全模式，需要检查集群配置"
    fi
fi
```

### 步骤3：验证修复

```bash
echo "========================================="
echo "验证HDFS功能"
echo "========================================="

# 测试基本HDFS操作
echo "测试HDFS基本操作..."

# 创建测试目录
hdfs dfs -mkdir /test_dir 2>/dev/null && echo "✓ 创建目录成功" || echo "✗ 创建目录失败"

# 列出根目录
hdfs dfs -ls / && echo "✓ 列出目录成功" || echo "✗ 列出目录失败"

# 删除测试目录
hdfs dfs -rmdir /test_dir 2>/dev/null && echo "✓ 删除目录成功" || echo "✗ 删除目录失败"

echo "HDFS功能验证完成"
```

## 🚀 针对您的项目的具体解决步骤

基于您的错误信息，请按以下顺序执行：

### 立即执行的命令

```bash
# 1. 检查当前安全模式状态
[root@hadoop01 movie-analysis-project]# hdfs dfsadmin -safemode get

# 2. 查看详细集群状态
[root@hadoop01 movie-analysis-project]# hdfs dfsadmin -report

# 3. 检查Hadoop进程
[root@hadoop01 movie-analysis-project]# jps

# 4. 等待2分钟让集群自动退出安全模式
[root@hadoop01 movie-analysis-project]# sleep 120

# 5. 再次检查安全模式状态
[root@hadoop01 movie-analysis-project]# hdfs dfsadmin -safemode get
```

### 如果仍然在安全模式，执行强制退出

```bash
# 强制退出安全模式
[root@hadoop01 movie-analysis-project]# hdfs dfsadmin -safemode leave

# 验证退出成功
[root@hadoop01 movie-analysis-project]# hdfs dfsadmin -safemode get

# 测试创建目录
[root@hadoop01 movie-analysis-project]# hdfs dfs -mkdir /input
```

### 继续您的项目部署

```bash
# 安全模式退出后，继续执行项目部署
[root@hadoop01 movie-analysis-project]# hdfs dfs -ls /

# 上传数据文件
[root@hadoop01 movie-analysis-project]# hdfs dfs -put input/movies.dat /input/
[root@hadoop01 movie-analysis-project]# hdfs dfs -put input/ratings.dat /input/
[root@hadoop01 movie-analysis-project]# hdfs dfs -put input/users.dat /input/

# 验证上传成功
[root@hadoop01 movie-analysis-project]# hdfs dfs -ls /input
```

## 📝 预防措施

为避免将来再次遇到安全模式问题：

1. **等待集群完全启动**：启动Hadoop后等待2-3分钟再进行操作
2. **监控磁盘空间**：确保DataNode磁盘使用率不超过90%
3. **检查网络连接**：确保所有节点网络通畅
4. **定期维护**：定期检查集群健康状态

## ⚡ 快速解决命令

如果您急于继续项目，可以直接执行：

```bash
# 一键解决安全模式问题
hdfs dfsadmin -safemode leave && echo "安全模式已关闭" || echo "退出安全模式失败"

# 立即测试
hdfs dfs -mkdir /input && echo "目录创建成功" || echo "目录创建失败"
```

执行这些命令后，您就可以继续进行MapReduce项目的部署了！
