package com.hadoop.movieanalysis.writable;

import org.apache.hadoop.io.Writable;
import java.io.DataInput;
import java.io.DataOutput;
import java.io.IOException;

/**
 * 评分统计信息
 * 包含评分总数、评分总和、最高评分、最低评分
 */
public class RatingStatistics implements Writable {
    private long count;          // 评分总数
    private double sum;          // 评分总和
    private double maxRating;    // 最高评分
    private double minRating;    // 最低评分

    public RatingStatistics() {
        this.count = 0;
        this.sum = 0.0;
        this.maxRating = Double.MIN_VALUE;
        this.minRating = Double.MAX_VALUE;
    }

    public RatingStatistics(long count, double sum, double maxRating, double minRating) {
        this.count = count;
        this.sum = sum;
        this.maxRating = maxRating;
        this.minRating = minRating;
    }

    /**
     * 添加一个评分到统计中
     * @param rating 评分值
     */
    public void addRating(double rating) {
        this.count++;
        this.sum += rating;
        this.maxRating = Math.max(this.maxRating, rating);
        this.minRating = Math.min(this.minRating, rating);
    }

    /**
     * 合并另一个统计对象
     * @param other 另一个RatingStatistics对象
     */
    public void merge(RatingStatistics other) {
        this.count += other.count;
        this.sum += other.sum;
        this.maxRating = Math.max(this.maxRating, other.maxRating);
        this.minRating = Math.min(this.minRating, other.minRating);
    }

    /**
     * 计算平均评分
     * @return 平均评分
     */
    public double getAverageRating() {
        return count > 0 ? sum / count : 0.0;
    }

    @Override
    public void write(DataOutput out) throws IOException {
        out.writeLong(count);
        out.writeDouble(sum);
        out.writeDouble(maxRating);
        out.writeDouble(minRating);
    }

    @Override
    public void readFields(DataInput in) throws IOException {
        count = in.readLong();
        sum = in.readDouble();
        maxRating = in.readDouble();
        minRating = in.readDouble();
    }

    @Override
    public String toString() {
        return String.format("Count: %d, Sum: %.2f, Avg: %.2f, Max: %.2f, Min: %.2f",
                count, sum, getAverageRating(), maxRating, minRating);
    }

    // Getters and Setters
    public long getCount() {
        return count;
    }

    public void setCount(long count) {
        this.count = count;
    }

    public double getSum() {
        return sum;
    }

    public void setSum(double sum) {
        this.sum = sum;
    }

    public double getMaxRating() {
        return maxRating;
    }

    public void setMaxRating(double maxRating) {
        this.maxRating = maxRating;
    }

    public double getMinRating() {
        return minRating;
    }

    public void setMinRating(double minRating) {
        this.minRating = minRating;
    }
}
