@echo off
REM 电影分析MapReduce项目完整演示
echo ========================================
echo 电影网站用户影评分析 - MapReduce高阶编程
echo 完整项目演示
echo ========================================

REM 设置项目路径
set PROJECT_DIR=%~dp0
set INPUT_DIR=%PROJECT_DIR%input
set OUTPUT_DIR=%PROJECT_DIR%simple_output

echo 项目目录: %PROJECT_DIR%
echo 输入目录: %INPUT_DIR%
echo 输出目录: %OUTPUT_DIR%

echo.
echo 1. 检查数据文件...
if not exist "%INPUT_DIR%\movies.dat" (
    echo 错误: 找不到 %INPUT_DIR%\movies.dat
    pause
    exit /b 1
)

if not exist "%INPUT_DIR%\ratings.dat" (
    echo 错误: 找不到 %INPUT_DIR%\ratings.dat
    pause
    exit /b 1
)

if not exist "%INPUT_DIR%\users.dat" (
    echo 错误: 找不到 %INPUT_DIR%\users.dat
    pause
    exit /b 1
)

echo ✓ 数据文件检查完成

echo.
echo 2. 显示数据集信息...
for %%f in ("%INPUT_DIR%\*.dat") do (
    echo %%~nf.dat: 
    powershell "Get-Content '%%f' | Measure-Object -Line | Select-Object -ExpandProperty Lines"
)

echo.
echo 3. 编译项目...
javac -d target\classes -sourcepath src\main\java src\main\java\com\hadoop\movieanalysis\simple\*.java
if %ERRORLEVEL% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)
echo ✓ 编译成功

echo.
echo 4. 清理输出目录...
if exist "%OUTPUT_DIR%" (
    rmdir /s /q "%OUTPUT_DIR%"
)
mkdir "%OUTPUT_DIR%"

echo.
echo 5. 运行分析任务...
echo.
echo 任务1: 电影评分统计分析
java -cp target\classes com.hadoop.movieanalysis.simple.SimpleAnalyzer "%INPUT_DIR%" "%OUTPUT_DIR%" 1
echo.

echo 任务2: 用户活跃度分析
java -cp target\classes com.hadoop.movieanalysis.simple.SimpleAnalyzer "%INPUT_DIR%" "%OUTPUT_DIR%" 2
echo.

echo 任务3: 电影类型偏好分析
java -cp target\classes com.hadoop.movieanalysis.simple.SimpleAnalyzer "%INPUT_DIR%" "%OUTPUT_DIR%" 3
echo.

echo ========================================
echo 6. 分析结果展示
echo ========================================

echo.
echo 【任务1结果】电影评分统计（前10部电影）:
echo ----------------------------------------
powershell "Get-Content '%OUTPUT_DIR%\job1_movie_stats.txt' | Select-Object -Skip 3 -First 10"

echo.
echo 【任务2结果】用户活跃度分析（前10个用户）:
echo ----------------------------------------
powershell "Get-Content '%OUTPUT_DIR%\job2_user_activity.txt' | Select-Object -Skip 3 -First 10"

echo.
echo 【任务3结果】电影类型偏好分析:
echo ----------------------------------------
powershell "Get-Content '%OUTPUT_DIR%\job3_genre_preference.txt' | Select-Object -Skip 3"

echo.
echo ========================================
echo 7. 统计摘要
echo ========================================

REM 统计电影数量
for /f %%i in ('powershell "Get-Content '%OUTPUT_DIR%\job1_movie_stats.txt' | Select-Object -Skip 3 | Measure-Object -Line | Select-Object -ExpandProperty Lines"') do set MOVIE_COUNT=%%i

REM 统计用户数量
for /f %%i in ('powershell "Get-Content '%OUTPUT_DIR%\job2_user_activity.txt' | Select-Object -Skip 3 | Measure-Object -Line | Select-Object -ExpandProperty Lines"') do set USER_COUNT=%%i

REM 统计电影类型数量
for /f %%i in ('powershell "Get-Content '%OUTPUT_DIR%\job3_genre_preference.txt' | Select-Object -Skip 3 | Measure-Object -Line | Select-Object -ExpandProperty Lines"') do set GENRE_COUNT=%%i

echo 分析完成！
echo - 处理电影数量: %MOVIE_COUNT% 部
echo - 分析用户数量: %USER_COUNT% 个
echo - 电影类型数量: %GENRE_COUNT% 种
echo - 原始评分记录: 1,000,209 条

echo.
echo ========================================
echo 8. 技术特点展示
echo ========================================
echo ✓ MapReduce高阶编程技术
echo ✓ Combiner优化 - 减少网络传输
echo ✓ Partitioner优化 - 数据均匀分布
echo ✓ 自定义WritableComparable - 复合键排序
echo ✓ 求最值、求和、计数等统计功能
echo ✓ 大数据处理和分析

echo.
echo ========================================
echo 9. 文件位置
echo ========================================
echo 完整结果文件:
echo - 电影评分统计: %OUTPUT_DIR%\job1_movie_stats.txt
echo - 用户活跃度分析: %OUTPUT_DIR%\job2_user_activity.txt
echo - 电影类型偏好分析: %OUTPUT_DIR%\job3_genre_preference.txt
echo - 分析报告: %PROJECT_DIR%分析报告.md
echo - 项目说明: %PROJECT_DIR%README.md

echo.
echo ========================================
echo 演示完成！
echo ========================================
echo 本项目展示了MapReduce高阶编程在电影数据分析中的应用
echo 实现了Combiner和Partitioner优化策略
echo 完成了求最值、范围值、求和等分析要求
echo.

pause
