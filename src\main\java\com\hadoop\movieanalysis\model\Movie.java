package com.hadoop.movieanalysis.model;

import org.apache.hadoop.io.Writable;
import java.io.DataInput;
import java.io.DataOutput;
import java.io.IOException;

/**
 * 电影数据模型
 * 格式: MovieID::Title::Genres
 */
public class Movie implements Writable {
    private int movieId;
    private String title;
    private String genres;

    public Movie() {
        // 默认构造函数
    }

    public Movie(int movieId, String title, String genres) {
        this.movieId = movieId;
        this.title = title;
        this.genres = genres;
    }

    /**
     * 从字符串解析电影信息
     * @param line 输入行，格式: MovieID::Title::Genres
     * @return Movie对象
     */
    public static Movie parseFromString(String line) {
        String[] parts = line.split("::");
        if (parts.length >= 3) {
            int movieId = Integer.parseInt(parts[0]);
            String title = parts[1];
            String genres = parts[2];
            return new Movie(movieId, title, genres);
        }
        return null;
    }

    @Override
    public void write(DataOutput out) throws IOException {
        out.writeInt(movieId);
        out.writeUTF(title);
        out.writeUTF(genres);
    }

    @Override
    public void readFields(DataInput in) throws IOException {
        movieId = in.readInt();
        title = in.readUTF();
        genres = in.readUTF();
    }

    // Getters and Setters
    public int getMovieId() {
        return movieId;
    }

    public void setMovieId(int movieId) {
        this.movieId = movieId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getGenres() {
        return genres;
    }

    public void setGenres(String genres) {
        this.genres = genres;
    }

    @Override
    public String toString() {
        return movieId + "::" + title + "::" + genres;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Movie movie = (Movie) obj;
        return movieId == movie.movieId;
    }

    @Override
    public int hashCode() {
        return Integer.hashCode(movieId);
    }
}
