#!/bin/bash
# MapReduce项目Hadoop集群编译脚本（无Maven环境）
# 适用于Hadoop 3.1.3 + Java 8+

echo "========================================="
echo "MapReduce项目Hadoop集群编译脚本"
echo "电影网站用户影评分析 - MapReduce高阶编程"
echo "========================================="

# 检查Hadoop环境
if [ -z "$HADOOP_HOME" ]; then
    echo "错误: HADOOP_HOME环境变量未设置"
    echo "请设置HADOOP_HOME指向您的Hadoop安装目录"
    exit 1
fi

if [ -z "$JAVA_HOME" ]; then
    echo "错误: JAVA_HOME环境变量未设置"
    echo "请设置JAVA_HOME指向您的Java安装目录"
    exit 1
fi

echo "Hadoop目录: $HADOOP_HOME"
echo "Java目录: $JAVA_HOME"
echo

# 创建编译目录
echo "1. 创建编译目录..."
mkdir -p target/classes
if [ $? -eq 0 ]; then
    echo "✓ 编译目录创建成功"
else
    echo "✗ 编译目录创建失败"
    exit 1
fi

# 设置Hadoop类路径
echo "2. 设置Hadoop编译环境..."
export HADOOP_CLASSPATH=$JAVA_HOME/lib/tools.jar
echo "✓ Hadoop类路径设置完成"

# 编译数据模型包
echo "3. 编译数据模型包..."
hadoop com.sun.tools.javac.Main -d target/classes -sourcepath src/main/java src/main/java/com/hadoop/movieanalysis/model/*.java
if [ $? -eq 0 ]; then
    echo "✓ 数据模型编译成功"
else
    echo "✗ 数据模型编译失败"
    exit 1
fi

# 编译自定义Writable类
echo "4. 编译自定义Writable类..."
hadoop com.sun.tools.javac.Main -d target/classes -sourcepath src/main/java src/main/java/com/hadoop/movieanalysis/writable/*.java
if [ $? -eq 0 ]; then
    echo "✓ Writable类编译成功"
else
    echo "✗ Writable类编译失败"
    exit 1
fi

# 编译MapReduce任务1
echo "5. 编译MapReduce任务1（电影评分统计）..."
hadoop com.sun.tools.javac.Main -d target/classes -sourcepath src/main/java src/main/java/com/hadoop/movieanalysis/job1/*.java
if [ $? -eq 0 ]; then
    echo "✓ 任务1编译成功"
else
    echo "✗ 任务1编译失败"
    exit 1
fi

# 编译MapReduce任务2
echo "6. 编译MapReduce任务2（用户活跃度分析）..."
hadoop com.sun.tools.javac.Main -d target/classes -sourcepath src/main/java src/main/java/com/hadoop/movieanalysis/job2/*.java
if [ $? -eq 0 ]; then
    echo "✓ 任务2编译成功"
else
    echo "✗ 任务2编译失败"
    exit 1
fi

# 编译MapReduce任务3
echo "7. 编译MapReduce任务3（电影类型偏好分析）..."
hadoop com.sun.tools.javac.Main -d target/classes -sourcepath src/main/java src/main/java/com/hadoop/movieanalysis/job3/*.java
if [ $? -eq 0 ]; then
    echo "✓ 任务3编译成功"
else
    echo "✗ 任务3编译失败"
    exit 1
fi

# 编译主驱动程序
echo "8. 编译主驱动程序..."
hadoop com.sun.tools.javac.Main -d target/classes -sourcepath src/main/java src/main/java/com/hadoop/movieanalysis/*.java
if [ $? -eq 0 ]; then
    echo "✓ 主驱动程序编译成功"
else
    echo "✗ 主驱动程序编译失败"
    exit 1
fi

# 创建JAR文件
echo "9. 创建JAR文件..."
cd target/classes
jar cf ../movie-analysis.jar com/
cd ../..

if [ -f "target/movie-analysis.jar" ]; then
    echo "✓ JAR文件创建成功: target/movie-analysis.jar"
    
    # 显示JAR文件信息
    echo
    echo "JAR文件信息:"
    ls -lh target/movie-analysis.jar
    echo
    echo "JAR文件内容:"
    jar tf target/movie-analysis.jar | head -10
    echo "..."
else
    echo "✗ JAR文件创建失败"
    exit 1
fi

echo
echo "========================================="
echo "编译完成！"
echo "========================================="
echo "JAR文件位置: target/movie-analysis.jar"
echo
echo "接下来的步骤:"
echo "1. 上传数据到HDFS:"
echo "   hdfs dfs -mkdir /input"
echo "   hdfs dfs -put input/*.dat /input/"
echo
echo "2. 提交任务到集群:"
echo "   hadoop jar target/movie-analysis.jar com.hadoop.movieanalysis.MovieAnalysisDriver /input /output all"
echo
echo "3. 查看结果:"
echo "   hdfs dfs -cat /output/job1_movie_stats/part-r-00000 | head -20"
echo "   hdfs dfs -cat /output/job2_user_activity/part-r-00000 | head -20"
echo "   hdfs dfs -cat /output/job3_genre_preference/part-r-00000"
echo
echo "========================================="
